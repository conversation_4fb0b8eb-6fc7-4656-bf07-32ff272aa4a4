#!/usr/bin/env python3
"""
调试ZigZag Fibonacci策略
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator

def create_simple_test_data():
    """创建简单的测试数据"""
    base_date = datetime(2024, 1, 1)
    
    # 简单的价格序列：明显的上涨-回撤-上涨模式
    prices = [
        100, 105, 110, 115, 120,  # 上涨20%
        115, 110, 105,            # 回撤到105 (约38.2%回撤)
        110, 115, 120, 125, 130   # 继续上涨到130
    ]
    
    stock_data = []
    for i, close_price in enumerate(prices):
        date = base_date + timedelta(days=i)
        
        # 简单的OHLC数据
        open_price = close_price - 0.5 if i % 2 == 0 else close_price + 0.5
        high_price = max(open_price, close_price) + 1
        low_price = min(open_price, close_price) - 1
        
        stock_data.append(StockData(
            symbol="TEST",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        ))
    
    return stock_data

def main():
    """主函数"""
    print("=== ZigZag Fibonacci 策略调试 ===\n")
    
    # 创建测试数据
    stock_data = create_simple_test_data()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 显示价格序列
    print("\n价格序列:")
    for i, data in enumerate(stock_data):
        print(f"{i:2d}. {data.date.strftime('%Y-%m-%d')} ¥{float(data.close):6.2f}")
    
    # 初始化信号生成器
    signal_generator = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5%阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618]
    )
    
    # 计算ZigZag点
    print(f"\n使用 {signal_generator.zigzag_threshold} 阈值计算ZigZag点:")
    zigzag_points = signal_generator._calculate_zigzag(stock_data)
    
    if zigzag_points:
        print("ZigZag转折点:")
        for i, (idx, price, point_type) in enumerate(zigzag_points):
            date = stock_data[idx].date
            print(f"{i+1:2d}. 索引{idx:2d} {date.strftime('%Y-%m-%d')} ¥{float(price):6.2f} ({point_type})")
    else:
        print("未找到ZigZag转折点")
    
    # 生成信号
    print(f"\n生成Fibonacci回撤信号:")
    signals = signal_generator.generate_signals(stock_data)
    
    if signals:
        print("交易信号:")
        for i, signal in enumerate(signals):
            print(f"{i+1:2d}. {signal.date.strftime('%Y-%m-%d')} {signal.signal_type:4s} "
                  f"¥{float(signal.price):6.2f} - {signal.reason} (置信度: {signal.confidence:.2f})")
    else:
        print("未生成任何信号")
    
    # 尝试不同的阈值
    print(f"\n尝试不同的ZigZag阈值:")
    for threshold in [0.03, 0.04, 0.05, 0.06, 0.08, 0.10]:
        test_generator = ZigZagFibonacciSignalGenerator(zigzag_threshold=threshold)
        test_zigzag = test_generator._calculate_zigzag(stock_data)
        test_signals = test_generator.generate_signals(stock_data)
        print(f"阈值 {threshold:.2f}: {len(test_zigzag)} 个ZigZag点, {len(test_signals)} 个信号")

if __name__ == "__main__":
    main()
