#!/usr/bin/env python3
"""
测试改进后的ZigZag计算和止损功能
"""

from datetime import datetime, timedelta
from decimal import Decimal
from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
import random

def create_test_data_with_volatility():
    """创建包含高波动性的测试数据，用于测试ZigZag和止损功能"""
    base_date = datetime(2024, 1, 1)
    stock_data = []
    
    # 创建一个有明显高低点的价格序列
    base_price = Decimal('100.0')
    
    # 价格模式：上涨 -> 下跌 -> 反弹 -> 大跌（触发止损）-> 恢复
    price_changes = [
        # 上涨阶段
        0.02, 0.03, 0.01, 0.04, 0.02, 0.03, 0.01, 0.02,  # 8天上涨约18%
        # 下跌阶段  
        -0.01, -0.02, -0.01, -0.03, -0.02,  # 5天下跌约9%
        # 反弹阶段
        0.02, 0.03, 0.01, 0.02,  # 4天反弹约8%
        # 大跌阶段（触发止损）
        -0.03, -0.04, -0.06, -0.05,  # 4天大跌约17%
        # 恢复阶段
        0.02, 0.04, 0.03, 0.02, 0.01, 0.02, 0.03  # 7天恢复约18%
    ]
    
    current_price = base_price
    
    for i, change in enumerate(price_changes):
        date = base_date + timedelta(days=i)
        
        # 计算当日价格
        price_change = current_price * Decimal(str(change))
        new_price = current_price + price_change
        
        # 生成OHLC数据，确保high >= close >= low
        if change > 0:  # 上涨日
            open_price = current_price
            close_price = new_price
            high_price = close_price + close_price * Decimal('0.01')  # 高点比收盘价高1%
            low_price = open_price - open_price * Decimal('0.005')   # 低点比开盘价低0.5%
        else:  # 下跌日
            open_price = current_price
            close_price = new_price
            high_price = open_price + open_price * Decimal('0.005')  # 高点比开盘价高0.5%
            low_price = close_price - close_price * Decimal('0.01')  # 低点比收盘价低1%
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        stock_data.append(StockData(
            symbol="TEST",
            date=date,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=random.randint(100000, 1000000)
        ))
        
        current_price = close_price
    
    return stock_data

def test_zigzag_improvements():
    """测试改进后的ZigZag计算"""
    print("=== 测试改进后的ZigZag计算 ===\n")
    
    # 创建测试数据
    stock_data = create_test_data_with_volatility()
    print(f"创建了 {len(stock_data)} 个数据点")
    
    # 显示价格数据
    print("\n价格数据:")
    for i, data in enumerate(stock_data):
        print(f"{i+1:2d}. {data.date.strftime('%m-%d')} "
              f"开:{float(data.open):6.2f} "
              f"高:{float(data.high):6.2f} "
              f"低:{float(data.low):6.2f} "
              f"收:{float(data.close):6.2f}")
    
    # 初始化ZigZag策略
    strategy = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5%阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
    )
    
    # 计算ZigZag点
    print(f"\n使用改进后的ZigZag计算 (阈值: {strategy.zigzag_threshold}):")
    zigzag_points = strategy._calculate_zigzag(stock_data)
    
    if zigzag_points:
        print("ZigZag转折点:")
        for i, (idx, price, point_type) in enumerate(zigzag_points):
            date = stock_data[idx].date
            print(f"{i+1:2d}. 索引{idx:2d} {date.strftime('%m-%d')} "
                  f"¥{float(price):6.2f} ({point_type})")
    else:
        print("未找到ZigZag转折点")
    
    # 生成交易信号
    print(f"\n生成交易信号:")
    signals = strategy.generate_signals(stock_data)
    
    if signals:
        print("交易信号:")
        for i, signal in enumerate(signals):
            print(f"{i+1:2d}. {signal.date.strftime('%m-%d')} {signal.signal_type:4s} "
                  f"¥{float(signal.price):6.2f} - {signal.reason} "
                  f"(置信度: {signal.confidence:.2f})")
    else:
        print("未生成交易信号")
    
    return stock_data, signals

def test_stop_loss_functionality():
    """测试止损功能"""
    print("\n\n=== 测试止损功能 ===\n")
    
    # 使用之前创建的数据
    stock_data, signals = test_zigzag_improvements()
    
    # 测试不同的止损设置
    stop_loss_settings = [
        {"stop_loss_pct": 0.03, "name": "3%止损"},
        {"stop_loss_pct": 0.05, "name": "5%止损"},
        {"stop_loss_pct": 0.08, "name": "8%止损"}
    ]
    
    for setting in stop_loss_settings:
        print(f"\n--- {setting['name']} ---")
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            signal_generator=ZigZagFibonacciSignalGenerator(zigzag_threshold=0.05),
            stop_loss_pct=setting['stop_loss_pct']
        )
        
        # 运行回测
        stock_data_dict = {"TEST": stock_data}
        results = backtest_engine.run_backtest(stock_data_dict)
        
        # 显示结果
        performance = results["TEST"]['performance']
        print(f"初始资金: ¥{float(performance['initial_capital']):,.2f}")
        print(f"最终价值: ¥{float(performance['final_value']):,.2f}")
        print(f"总收益率: {performance['total_return']:.2%}")
        print(f"交易次数: {len(performance['trades'])}")
        print(f"止损次数: {performance['stop_loss_count']}")
        
        # 显示交易详情
        if performance['trades']:
            print("交易记录:")
            for i, trade in enumerate(performance['trades']):
                trade_type = trade['type']
                if trade_type == 'STOP_LOSS':
                    print(f"  {i+1}. {trade['date'].strftime('%m-%d')} {trade_type} "
                          f"{trade['shares']}股 @¥{float(trade['price']):.2f} - {trade['reason']}")
                else:
                    print(f"  {i+1}. {trade['date'].strftime('%m-%d')} {trade_type} "
                          f"{trade['shares']}股 @¥{float(trade['price']):.2f} - {trade['reason']}")

def main():
    """主函数"""
    print("测试ZigZag计算改进和止损功能\n")
    print("=" * 60)
    
    # 测试ZigZag改进
    test_zigzag_improvements()
    
    # 测试止损功能
    test_stop_loss_functionality()
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    main()
