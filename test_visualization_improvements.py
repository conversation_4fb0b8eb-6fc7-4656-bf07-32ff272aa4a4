#!/usr/bin/env python3
"""
测试可视化改进效果
"""

from datetime import datetime, timedelta
from decimal import Decimal
from stock_backtest.data_models import StockData, TradingSignal
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer
import random
import math

def create_test_data_with_signals():
    """创建包含明显交易信号的测试数据"""
    base_date = datetime(2024, 1, 1)
    stock_data = []
    
    # 创建一个有明显波动的价格序列，确保能产生交易信号
    base_price = 100.0
    days = 50
    
    for i in range(days):
        date = base_date + timedelta(days=i)
        
        # 创建波浪形价格模式
        if i < 15:
            # 上涨阶段
            price = base_price * (1.0 + i * 0.03)
        elif i < 25:
            # 下跌阶段
            price = base_price * (1.45 - (i - 15) * 0.04)
        elif i < 35:
            # 反弹阶段
            price = base_price * (1.05 + (i - 25) * 0.025)
        else:
            # 最终下跌
            price = base_price * (1.3 - (i - 35) * 0.02)
        
        # 添加随机波动
        price *= (1.0 + random.uniform(-0.02, 0.02))
        price_decimal = Decimal(str(round(price, 2)))
        
        # 生成OHLC数据
        daily_volatility = price * 0.015
        open_price = price_decimal
        close_price = price_decimal
        high_price = price_decimal + Decimal(str(round(random.uniform(0, daily_volatility), 2)))
        low_price = price_decimal - Decimal(str(round(random.uniform(0, daily_volatility), 2)))
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        stock_data.append(StockData(
            symbol="TEST_VIS",
            date=date,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=random.randint(100000, 1000000)
        ))
    
    return stock_data

def create_manual_signals(stock_data):
    """创建一些手动交易信号用于测试可视化"""
    signals = []
    
    # 在第10天买入
    if len(stock_data) > 10:
        signals.append(TradingSignal(
            symbol="TEST_VIS",
            date=stock_data[10].date,
            signal_type='BUY',
            price=stock_data[10].close,
            reason='测试买入信号',
            confidence=0.8
        ))
    
    # 在第20天卖出
    if len(stock_data) > 20:
        signals.append(TradingSignal(
            symbol="TEST_VIS",
            date=stock_data[20].date,
            signal_type='SELL',
            price=stock_data[20].close,
            reason='测试卖出信号',
            confidence=0.7
        ))
    
    # 在第30天再次买入
    if len(stock_data) > 30:
        signals.append(TradingSignal(
            symbol="TEST_VIS",
            date=stock_data[30].date,
            signal_type='BUY',
            price=stock_data[30].close,
            reason='测试买入信号2',
            confidence=0.75
        ))
    
    return signals

def test_improved_visualization():
    """测试改进后的可视化效果"""
    print("=== 测试改进后的可视化效果 ===\n")
    
    # 1. 创建测试数据
    print("1. 创建测试数据...")
    stock_data = create_test_data_with_signals()
    print(f"   创建了 {len(stock_data)} 天的数据")
    
    # 2. 创建手动交易信号
    print("\n2. 创建交易信号...")
    signals = create_manual_signals(stock_data)
    print(f"   创建了 {len(signals)} 个交易信号")
    
    # 3. 计算ZigZag点
    print("\n3. 计算ZigZag转折点...")
    strategy = ZigZagFibonacciSignalGenerator(zigzag_threshold=0.06)
    zigzag_points = strategy._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个转折点")
    
    # 4. 运行回测
    print("\n4. 运行回测...")
    backtest_engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        stop_loss_pct=0.05
    )
    
    # 直接使用手动信号进行回测
    performance = backtest_engine._execute_backtest(stock_data, signals)
    
    print(f"   初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"   最终价值: ¥{float(performance['final_value']):,.2f}")
    print(f"   总收益率: {performance['total_return']:.2%}")
    print(f"   交易次数: {len(performance['trades'])}")
    print(f"   止损次数: {performance['stop_loss_count']}")
    
    # 显示交易详情
    if performance['trades']:
        print("\n   交易记录:")
        for i, trade in enumerate(performance['trades']):
            trade_type = trade['type']
            if trade_type == 'BUY':
                print(f"   {i+1}. {trade['date'].strftime('%Y-%m-%d')} 买入 "
                      f"{trade['shares']}股 @¥{float(trade['price']):.2f}")
            elif trade_type == 'SELL':
                print(f"   {i+1}. {trade['date'].strftime('%Y-%m-%d')} 卖出 "
                      f"{trade['shares']}股 @¥{float(trade['price']):.2f}")
            elif trade_type == 'STOP_LOSS':
                print(f"   {i+1}. {trade['date'].strftime('%Y-%m-%d')} 止损 "
                      f"{trade['shares']}股 @¥{float(trade['price']):.2f}")
    
    # 5. 创建改进后的可视化图表
    print("\n5. 创建改进后的可视化图表...")
    visualizer = StockChartVisualizer()
    
    chart = visualizer.create_kline_chart(
        stock_data=stock_data,
        signals=signals,
        trades=performance['trades'],
        symbol="TEST_VIS",
        zigzag_points=zigzag_points
    )
    
    # 保存图表
    filename = "improved_visualization_test.html"
    
    # 创建结果字典用于摘要
    results = {
        'data': stock_data,
        'signals': signals,
        'performance': performance,
        'zigzag_points': zigzag_points
    }
    
    visualizer.save_chart_with_summary(chart, results, filename)
    
    print(f"   图表已保存到: {filename}")
    print("\n   可视化改进效果:")
    print("   ✅ 买入标记: 蓝色菱形 ◆ (不再是三角形)")
    print("   ✅ 卖出标记: 橙色方形 ■ (不再是三角形)")
    print("   ✅ ZigZag高点: 红色向上三角形 ▲ (无价格标签)")
    print("   ✅ ZigZag低点: 绿色向下三角形 ▼ (无价格标签)")
    print("   ✅ 去掉了买卖区间的矩形框")
    print("   ✅ ZigZag连线: 红色虚线")
    
    # 打开浏览器查看
    try:
        import webbrowser
        import os
        file_path = os.path.abspath(filename)
        webbrowser.open(f'file://{file_path}')
        print(f"\n   正在打开浏览器查看改进效果...")
    except:
        print(f"\n   请手动打开 {filename} 查看改进效果")

def main():
    """主函数"""
    print("可视化改进效果测试")
    print("=" * 50)
    
    test_improved_visualization()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("主要改进:")
    print("1. 买入卖出符号改为菱形和方形，与ZigZag三角形区分")
    print("2. 去掉了ZigZag点的价格显示")
    print("3. 去掉了买卖区间的矩形框，图表更清爽")

if __name__ == "__main__":
    main()
