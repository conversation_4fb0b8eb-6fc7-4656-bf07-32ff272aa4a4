#!/usr/bin/env python3
"""
ZigZag Fibonacci 策略回测演示

展示如何使用改造后的BacktestEngine和可视化功能
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer

def create_demo_data():
    """创建演示用的股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 创建一个包含明显趋势和回撤的价格序列
    price_sequence = [
        # 初始上涨
        100, 103, 106, 109, 112, 115, 118, 121, 124, 127, 130,
        # 第一次回撤（约38.2%）
        128, 125, 122, 119, 116, 118, 121, 124, 127, 130, 133, 136,
        # 第二次回撤（约50%）
        134, 131, 128, 125, 127, 130, 133, 136, 139, 142, 145,
        # 主要回撤（约61.8%）
        143, 140, 137, 134, 131, 128, 125, 122, 119, 116,
        # 反弹
        118, 121, 124, 127, 130, 133, 136, 139, 142, 145, 148
    ]
    
    stock_data = []
    for i, close_price in enumerate(price_sequence):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        if i == 0:
            open_price = close_price
        else:
            prev_close = price_sequence[i-1]
            gap = (close_price - prev_close) * 0.2
            open_price = prev_close + gap
        
        high_price = max(open_price, close_price) + abs(close_price * 0.01)
        low_price = min(open_price, close_price) - abs(close_price * 0.01)
        
        # 成交量
        volume = int(1000000 * (1 + abs(close_price - price_sequence[0]) / price_sequence[0]))
        
        stock_data.append(StockData(
            symbol="DEMO",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(round(close_price, 2))),
            volume=volume
        ))
    
    return stock_data

def main():
    """主函数"""
    print("=== ZigZag Fibonacci 策略回测演示 ===\n")
    
    # 1. 创建演示数据
    print("1. 创建演示数据...")
    stock_data = create_demo_data()
    print(f"   数据点数: {len(stock_data)}")
    print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
    
    # 2. 初始化ZigZag Fibonacci策略
    print("\n2. 初始化ZigZag Fibonacci策略...")
    zigzag_strategy = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5%阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
    )
    print(f"   ZigZag阈值: {float(zigzag_strategy.zigzag_threshold):.1%}")
    
    # 3. 初始化回测引擎（使用ZigZag策略）
    print("\n3. 初始化回测引擎...")
    backtest_engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        signal_generator=zigzag_strategy
    )
    
    # 4. 运行回测
    print("\n4. 运行回测...")
    stock_data_dict = {"DEMO": stock_data}
    results = backtest_engine.run_backtest(stock_data_dict)
    
    # 提取结果
    demo_results = results["DEMO"]
    signals = demo_results['signals']
    trades = demo_results['performance']['trades']
    zigzag_points = demo_results.get('zigzag_points', [])
    
    print(f"   生成信号: {len(signals)}个")
    print(f"   执行交易: {len(trades)}次")
    print(f"   ZigZag转折点: {len(zigzag_points)}个")
    
    # 5. 显示回测结果
    print("\n5. 回测结果:")
    performance = demo_results['performance']
    print(f"   初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"   最终价值: ¥{float(performance['final_value']):,.2f}")
    print(f"   总收益率: {float(performance['total_return']):.2%}")
    
    # 6. 显示信号详情
    if signals:
        print("\n6. 交易信号:")
        for i, signal in enumerate(signals):
            print(f"   {i+1}. {signal.date.strftime('%m-%d')} {signal.signal_type:4s} "
                  f"¥{float(signal.price):6.2f} - {signal.reason}")
    
    # 7. 显示ZigZag转折点
    if zigzag_points:
        print("\n7. ZigZag转折点:")
        for i, (idx, price, point_type) in enumerate(zigzag_points):
            date = stock_data[idx].date
            print(f"   {i+1:2d}. {date.strftime('%m-%d')} ¥{float(price):6.2f} ({point_type})")
    
    # 8. 生成可视化图表
    print("\n8. 生成可视化图表...")
    try:
        visualizer = StockChartVisualizer()
        
        # 创建包含ZigZag线的图表
        chart = visualizer.create_kline_chart(
            stock_data=demo_results['data'],
            signals=signals,
            trades=trades,
            symbol="DEMO",
            zigzag_points=zigzag_points  # 传入ZigZag点
        )
        
        # 保存图表
        filename = "zigzag_fibonacci_demo.html"
        visualizer.save_chart_with_summary(chart, demo_results, filename)
        print(f"   图表已保存到: {filename}")
        print("   图表包含以下元素:")
        print("   - K线图")
        print("   - ZigZag转折点连线（红色虚线）")
        print("   - 交易信号标记")
        print("   - 成交量")
        print("   - 净值曲线")
        
    except Exception as e:
        print(f"   图表生成失败: {e}")
    
    # 9. 策略评估
    print("\n9. 策略评估:")
    buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
    strategy_return = float(performance['total_return'])
    
    print(f"   买入持有收益率: {buy_hold_return:.2%}")
    print(f"   策略收益率: {strategy_return:.2%}")
    print(f"   超额收益: {strategy_return - buy_hold_return:.2%}")
    
    if strategy_return > buy_hold_return:
        print("   ✓ 策略跑赢买入持有")
    else:
        print("   ✗ 策略跑输买入持有")
    
    # 10. 对比传统策略
    print("\n10. 对比传统策略:")
    try:
        from stock_backtest.signal_generator import SignalGenerator
        
        # 使用传统策略进行回测
        traditional_engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            signal_generator=SignalGenerator()  # 传统MACD+布林线策略
        )
        
        traditional_results = traditional_engine.run_backtest(stock_data_dict)
        traditional_performance = traditional_results["DEMO"]['performance']
        traditional_return = float(traditional_performance['total_return'])
        
        print(f"   传统策略收益率: {traditional_return:.2%}")
        print(f"   ZigZag策略收益率: {strategy_return:.2%}")
        
        if strategy_return > traditional_return:
            print("   ✓ ZigZag策略优于传统策略")
        else:
            print("   ✗ ZigZag策略不如传统策略")
            
    except Exception as e:
        print(f"   传统策略对比失败: {e}")
    
    print(f"\n策略总结:")
    print(f"- ZigZag成功识别了 {len(zigzag_points)} 个关键转折点")
    print(f"- 基于Fibonacci回撤生成了 {len(signals)} 个交易信号")
    print(f"- 最终收益率: {strategy_return:.2%}")
    print(f"- 可视化图表已保存，可以查看详细的交易过程")

if __name__ == "__main__":
    main()
