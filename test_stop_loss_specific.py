#!/usr/bin/env python3
"""
专门测试止损功能的脚本
"""

from datetime import datetime, timedelta
from decimal import Decimal
from stock_backtest.data_models import StockData, TradingSignal
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.signal_generator import SignalGenerator

def create_stop_loss_test_data():
    """创建专门用于测试止损的数据"""
    base_date = datetime(2024, 1, 1)
    stock_data = []
    
    # 创建一个简单的价格序列：买入后价格下跌触发止损
    prices = [
        100.0,  # 第1天 - 基准价格
        102.0,  # 第2天 - 买入信号日（上涨2%）
        101.0,  # 第3天 - 小幅下跌
        99.0,   # 第4天 - 下跌3%
        96.0,   # 第5天 - 下跌6%（应该触发5%止损）
        94.0,   # 第6天 - 继续下跌
        98.0,   # 第7天 - 反弹
        100.0,  # 第8天 - 回到原价
    ]
    
    for i, price in enumerate(prices):
        date = base_date + timedelta(days=i)
        price_decimal = Decimal(str(price))
        
        # 生成OHLC数据
        open_price = price_decimal
        close_price = price_decimal
        high_price = price_decimal * Decimal('1.01')  # 高1%
        low_price = price_decimal * Decimal('0.99')   # 低1%
        
        stock_data.append(StockData(
            symbol="STOP_TEST",
            date=date,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=100000
        ))
    
    return stock_data

def create_manual_signals():
    """创建手动交易信号用于测试"""
    base_date = datetime(2024, 1, 1)
    
    # 在第2天创建买入信号
    buy_signal = TradingSignal(
        symbol="STOP_TEST",
        date=base_date + timedelta(days=1),  # 第2天
        signal_type='BUY',
        price=Decimal('102.0'),
        reason='测试买入信号',
        confidence=0.8
    )
    
    return [buy_signal]

def test_stop_loss_execution():
    """测试止损执行"""
    print("=== 专门测试止损功能 ===\n")
    
    # 创建测试数据
    stock_data = create_stop_loss_test_data()
    signals = create_manual_signals()
    
    print("测试数据:")
    for i, data in enumerate(stock_data):
        print(f"{i+1}. {data.date.strftime('%m-%d')} "
              f"开:{float(data.open):6.2f} "
              f"高:{float(data.high):6.2f} "
              f"低:{float(data.low):6.2f} "
              f"收:{float(data.close):6.2f}")
    
    print(f"\n交易信号:")
    for signal in signals:
        print(f"- {signal.date.strftime('%m-%d')} {signal.signal_type} @¥{float(signal.price):.2f}")
    
    # 测试不同止损设置
    stop_loss_tests = [
        {"stop_loss_pct": 0.03, "name": "3%止损"},
        {"stop_loss_pct": 0.05, "name": "5%止损"},
        {"stop_loss_pct": 0.08, "name": "8%止损"}
    ]
    
    for test_config in stop_loss_tests:
        print(f"\n--- {test_config['name']} ---")
        
        # 创建自定义回测引擎，直接传入信号
        backtest_engine = BacktestEngine(
            initial_capital=100000,
            commission_rate=0.001,
            stop_loss_pct=test_config['stop_loss_pct']
        )
        
        # 直接调用回测执行方法
        performance = backtest_engine._execute_backtest(stock_data, signals)
        
        print(f"初始资金: ¥{float(performance['initial_capital']):,.2f}")
        print(f"最终价值: ¥{float(performance['final_value']):,.2f}")
        print(f"总收益率: {performance['total_return']:.2%}")
        print(f"交易次数: {len(performance['trades'])}")
        print(f"止损次数: {performance['stop_loss_count']}")
        print(f"止损设置: {performance['stop_loss_pct']:.1%}")
        
        # 显示详细交易记录
        if performance['trades']:
            print("交易记录:")
            for i, trade in enumerate(performance['trades']):
                trade_type = trade['type']
                if trade_type == 'STOP_LOSS':
                    print(f"  {i+1}. {trade['date'].strftime('%m-%d')} {trade_type} "
                          f"{trade['shares']}股 @¥{float(trade['price']):.2f} - {trade['reason']}")
                elif trade_type == 'BUY':
                    print(f"  {i+1}. {trade['date'].strftime('%m-%d')} {trade_type} "
                          f"{trade['shares']}股 @¥{float(trade['price']):.2f} "
                          f"成本:¥{float(trade['cost']):.2f}")
                elif trade_type == 'SELL':
                    print(f"  {i+1}. {trade['date'].strftime('%m-%d')} {trade_type} "
                          f"{trade['shares']}股 @¥{float(trade['price']):.2f} "
                          f"收入:¥{float(trade['proceeds']):.2f}")

def main():
    """主函数"""
    test_stop_loss_execution()

if __name__ == "__main__":
    main()
