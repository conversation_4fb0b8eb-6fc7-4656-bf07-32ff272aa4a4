#!/usr/bin/env python3
"""
测试改进后的布林线（9天平均线，1倍标准差）
"""

from datetime import datetime, timedelta
from decimal import Decimal
from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer
from stock_backtest.indicators import TechnicalIndicators
import random
import math

def create_trending_data():
    """创建有趋势的股票数据用于测试布林线"""
    base_date = datetime(2024, 1, 1)
    stock_data = []
    
    # 创建一个有明显趋势和波动的价格序列
    base_price = 100.0
    days = 60
    
    for i in range(days):
        date = base_date + timedelta(days=i)
        
        # 创建趋势 + 周期性波动
        trend = base_price * (1.0 + i * 0.005)  # 缓慢上涨趋势
        cycle = 10 * math.sin(i * 0.2)  # 周期性波动
        noise = random.uniform(-3, 3)  # 随机噪音
        
        price = trend + cycle + noise
        price = max(price, 50)  # 确保价格不会太低
        price_decimal = Decimal(str(round(price, 2)))
        
        # 生成OHLC数据
        daily_volatility = price * 0.02
        open_price = price_decimal
        close_price = price_decimal
        high_price = price_decimal + Decimal(str(round(random.uniform(0, daily_volatility), 2)))
        low_price = price_decimal - Decimal(str(round(random.uniform(0, daily_volatility), 2)))
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        stock_data.append(StockData(
            symbol="BB_TEST",
            date=date,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=random.randint(100000, 1000000)
        ))
    
    return stock_data

def test_bollinger_bands():
    """测试布林线计算和可视化"""
    print("=== 测试改进后的布林线 ===\n")
    
    # 1. 创建测试数据
    print("1. 创建测试数据...")
    stock_data = create_trending_data()
    print(f"   创建了 {len(stock_data)} 天的数据")
    print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
    
    # 2. 手动计算布林线进行验证
    print("\n2. 计算布林线...")
    prices = [item.close for item in stock_data]
    
    # 计算新的布林线参数（9天，1倍标准差）
    bb_upper_new, bb_middle_new, bb_lower_new = TechnicalIndicators.calculate_bollinger_bands(
        prices, period=9, std_dev=1
    )
    
    # 计算传统布林线参数（20天，2倍标准差）用于对比
    bb_upper_old, bb_middle_old, bb_lower_old = TechnicalIndicators.calculate_bollinger_bands(
        prices, period=20, std_dev=2
    )
    
    print("   布林线参数对比:")
    print("   - 新参数: 9天平均线，1倍标准差")
    print("   - 旧参数: 20天平均线，2倍标准差")
    
    # 显示最后几天的布林线数值
    print("\n   最后5天的布林线数值:")
    print("   日期        收盘价   新上轨   新中轨   新下轨   旧上轨   旧中轨   旧下轨")
    print("   " + "-" * 75)
    
    for i in range(-5, 0):
        date = stock_data[i].date
        close = float(stock_data[i].close)
        new_upper = float(bb_upper_new[i]) if bb_upper_new[i] else None
        new_middle = float(bb_middle_new[i]) if bb_middle_new[i] else None
        new_lower = float(bb_lower_new[i]) if bb_lower_new[i] else None
        old_upper = float(bb_upper_old[i]) if bb_upper_old[i] else None
        old_middle = float(bb_middle_old[i]) if bb_middle_old[i] else None
        old_lower = float(bb_lower_old[i]) if bb_lower_old[i] else None
        
        print(f"   {date.strftime('%m-%d')}      {close:7.2f}  "
              f"{new_upper:7.2f if new_upper else '   N/A'}  "
              f"{new_middle:7.2f if new_middle else '   N/A'}  "
              f"{new_lower:7.2f if new_lower else '   N/A'}  "
              f"{old_upper:7.2f if old_upper else '   N/A'}  "
              f"{old_middle:7.2f if old_middle else '   N/A'}  "
              f"{old_lower:7.2f if old_lower else '   N/A'}")
    
    # 3. 初始化ZigZag策略
    print("\n3. 初始化ZigZag策略...")
    strategy = ZigZagFibonacciSignalGenerator(zigzag_threshold=0.08)
    zigzag_points = strategy._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个ZigZag转折点")
    
    # 4. 运行回测（这会自动计算新的布林线参数）
    print("\n4. 运行回测...")
    backtest_engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        signal_generator=strategy,
        stop_loss_pct=0.05
    )
    
    stock_data_dict = {"BB_TEST": stock_data}
    results = backtest_engine.run_backtest(stock_data_dict)
    
    bb_results = results["BB_TEST"]
    performance = bb_results['performance']
    processed_data = bb_results['data']  # 包含计算后的布林线数据
    
    print(f"   初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"   最终价值: ¥{float(performance['final_value']):,.2f}")
    print(f"   总收益率: {performance['total_return']:.2%}")
    print(f"   交易次数: {len(performance['trades'])}")
    
    # 5. 创建可视化图表
    print("\n5. 创建可视化图表...")
    visualizer = StockChartVisualizer()
    
    chart = visualizer.create_kline_chart(
        stock_data=processed_data,  # 使用包含布林线数据的处理后数据
        signals=bb_results['signals'],
        trades=performance['trades'],
        symbol="BB_TEST",
        zigzag_points=zigzag_points
    )
    
    # 保存图表
    filename = "bollinger_bands_test.html"
    visualizer.save_chart_with_summary(chart, bb_results, filename)
    
    print(f"   图表已保存到: {filename}")
    print("\n   布林线可视化特性:")
    print("   ✅ 布林上轨: 红色虚线（9天均线 + 1倍标准差）")
    print("   ✅ 布林中轨: 蓝色实线（9天移动平均线）")
    print("   ✅ 布林下轨: 绿色虚线（9天均线 - 1倍标准差）")
    print("   ✅ 更敏感的布林线，能更快反应价格变化")
    print("   ✅ 图例显示参数信息")
    
    # 6. 分析布林线效果
    print("\n6. 布林线效果分析:")
    
    # 计算价格触及布林线的次数
    touch_upper = 0
    touch_lower = 0
    within_bands = 0
    
    for i, data in enumerate(processed_data):
        if data.bb_upper and data.bb_lower:
            if data.high >= data.bb_upper:
                touch_upper += 1
            if data.low <= data.bb_lower:
                touch_lower += 1
            if data.bb_lower <= data.close <= data.bb_upper:
                within_bands += 1
    
    total_valid_days = len([d for d in processed_data if d.bb_upper and d.bb_lower])
    
    print(f"   - 有效计算天数: {total_valid_days}")
    print(f"   - 触及上轨次数: {touch_upper} ({touch_upper/total_valid_days*100:.1f}%)")
    print(f"   - 触及下轨次数: {touch_lower} ({touch_lower/total_valid_days*100:.1f}%)")
    print(f"   - 价格在轨道内: {within_bands} ({within_bands/total_valid_days*100:.1f}%)")
    
    # 打开浏览器查看
    try:
        import webbrowser
        import os
        file_path = os.path.abspath(filename)
        webbrowser.open(f'file://{file_path}')
        print(f"\n   正在打开浏览器查看布林线效果...")
    except:
        print(f"\n   请手动打开 {filename} 查看布林线效果")

def main():
    """主函数"""
    print("布林线改进测试")
    print("=" * 50)
    
    test_bollinger_bands()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("布林线已改进为:")
    print("- 9天移动平均线（中轨）")
    print("- 上下轨为中轨 ± 1倍标准差")
    print("- 更敏感，能更快反应价格变化")

if __name__ == "__main__":
    main()
