#!/usr/bin/env python3
"""
ZigZag Fibonacci 策略完整回测示例

这个脚本演示如何使用ZigZag Fibonacci策略进行完整的股票回测
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer

def create_realistic_stock_data():
    """创建更真实的股票数据（模拟一个完整的趋势周期）"""
    base_date = datetime(2024, 1, 1)
    
    # 模拟更复杂的股价走势：包含多个趋势和回撤
    # 基础价格序列，包含明显的趋势和Fibonacci回撤点
    base_prices = [
        # 初始上涨趋势
        100, 102, 105, 108, 110, 113, 115, 118, 120, 122,
        # 第一次回撤（约38.2%回撤）
        119, 116, 114, 117, 120, 123, 126, 129, 132, 135,
        # 第二次回撤（约50%回撤）
        133, 130, 127, 125, 128, 131, 134, 137, 140, 143,
        # 第三次回撤（约61.8%回撤）
        141, 138, 135, 132, 135, 138, 141, 144, 147, 150,
        # 趋势转换点 - 开始下跌
        148, 145, 142, 139, 136, 133, 130, 127, 124, 121,
        # 下跌中的反弹（约38.2%反弹）
        123, 126, 129, 127, 124, 121, 118, 115, 112, 109,
        # 继续下跌
        107, 104, 101, 98, 95, 92, 89, 86, 83, 80,
        # 底部反弹
        82, 85, 88, 91, 94, 97, 100, 103, 106, 109,
        # 新的上涨趋势开始
        112, 115, 118, 121, 124, 127, 130, 133, 136, 139
    ]
    
    stock_data = []
    for i, close_price in enumerate(base_prices):
        date = base_date + timedelta(days=i)
        
        # 添加一些随机波动使数据更真实
        import random
        random.seed(i)  # 确保结果可重现
        
        # 模拟开盘价（基于前一日收盘价）
        if i == 0:
            open_price = close_price
        else:
            prev_close = base_prices[i-1]
            gap = (close_price - prev_close) * 0.3  # 跳空幅度
            open_price = prev_close + gap + random.uniform(-0.5, 0.5)
        
        # 模拟最高价和最低价
        daily_range = abs(close_price * 0.02)  # 日内波动2%
        high_price = max(open_price, close_price) + random.uniform(0, daily_range)
        low_price = min(open_price, close_price) - random.uniform(0, daily_range)
        
        # 模拟成交量（价格上涨时成交量增加）
        if i > 0:
            price_change = close_price - base_prices[i-1]
            base_volume = 1000000
            volume_multiplier = 1 + abs(price_change) / close_price
            volume = int(base_volume * volume_multiplier * random.uniform(0.8, 1.2))
        else:
            volume = 1000000
        
        stock_data.append(StockData(
            symbol="ZIGZAG_TEST",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(round(close_price, 2))),
            volume=volume
        ))
    
    return stock_data

def main():
    """主函数"""
    print("=== ZigZag Fibonacci 策略完整回测 ===\n")
    
    # 创建股票数据
    print("1. 创建股票数据...")
    stock_data = create_realistic_stock_data()
    print(f"   创建了 {len(stock_data)} 个交易日数据")
    print(f"   时间范围: {stock_data[0].date.strftime('%Y-%m-%d')} 到 {stock_data[-1].date.strftime('%Y-%m-%d')}")
    
    # 初始化ZigZag Fibonacci信号生成器
    print("\n2. 初始化 ZigZag Fibonacci 信号生成器...")
    signal_generator = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5% 的ZigZag阈值（更敏感）
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]  # 完整的Fibonacci水平
    )
    print("   配置参数:")
    print(f"   - ZigZag阈值: {signal_generator.zigzag_threshold}")
    print(f"   - Fibonacci水平: {signal_generator.fib_levels}")
    
    # 生成交易信号
    print("\n3. 生成交易信号...")

    # 先检查ZigZag点
    zigzag_points = signal_generator._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个ZigZag点")

    signals = signal_generator.generate_signals(stock_data)
    print(f"   生成了 {len(signals)} 个交易信号")
    
    if signals:
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        print(f"   - 买入信号: {len(buy_signals)}")
        print(f"   - 卖出信号: {len(sell_signals)}")
        
        # 显示前几个信号
        print("\n   前5个信号:")
        for i, signal in enumerate(signals[:5]):
            print(f"   {i+1}. {signal.date.strftime('%Y-%m-%d')} {signal.signal_type} "
                  f"¥{float(signal.price):.2f} - {signal.reason} (置信度: {signal.confidence:.2f})")
    
    # 运行回测
    print("\n4. 运行回测...")
    backtest_engine = BacktestEngine(
        initial_capital=100000,  # 10万初始资金
        commission_rate=0.001    # 0.1% 手续费
    )

    # BacktestEngine期望的是字典格式
    stock_data_dict = {"ZIGZAG_TEST": stock_data}
    results = backtest_engine.run_backtest(stock_data_dict)

    # 提取单个股票的结果
    stock_results = results["ZIGZAG_TEST"]
    
    # 显示回测结果
    print("\n5. 回测结果:")
    print("-" * 60)
    performance = stock_results['performance']
    print(f"初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"最终价值: ¥{float(performance['final_value']):,.2f}")
    total_profit = float(performance['final_value']) - float(performance['initial_capital'])
    print(f"总收益: ¥{total_profit:,.2f}")
    print(f"总收益率: {performance['total_return']:.2%}")
    print(f"交易次数: {len(performance['trades'])}")
    
    if performance['trades']:
        profitable_trades = [t for t in performance['trades'] if t['profit'] > 0]
        print(f"盈利交易: {len(profitable_trades)}/{len(performance['trades'])} "
              f"({len(profitable_trades)/len(performance['trades']):.1%})")
        
        if profitable_trades:
            avg_profit = sum(t['profit'] for t in profitable_trades) / len(profitable_trades)
            print(f"平均盈利: ¥{avg_profit:.2f}")
        
        losing_trades = [t for t in performance['trades'] if t['profit'] <= 0]
        if losing_trades:
            avg_loss = sum(t['profit'] for t in losing_trades) / len(losing_trades)
            print(f"平均亏损: ¥{avg_loss:.2f}")
    
    # 显示ZigZag分析
    print("\n6. ZigZag 分析:")
    zigzag_points = signal_generator._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个ZigZag转折点")
    
    if zigzag_points:
        print("   主要转折点:")
        for i, (idx, price, point_type) in enumerate(zigzag_points[:8]):  # 显示前8个
            date = stock_data[idx].date
            print(f"   {i+1}. {date.strftime('%Y-%m-%d')} ¥{float(price):.2f} ({point_type})")
    
    # 生成可视化图表
    print("\n7. 生成可视化图表...")
    try:
        visualizer = StockChartVisualizer()
        chart = visualizer.create_kline_chart(
            stock_data=stock_data,
            signals=signals,
            trades=performance['trades'],
            symbol="ZIGZAG_TEST"
        )
        
        # 保存图表
        filename = "zigzag_fibonacci_backtest.html"
        visualizer.save_chart_with_summary(chart, stock_results, filename)
        print(f"   图表已保存到: {filename}")
        
    except Exception as e:
        print(f"   图表生成失败: {e}")
    
    # 策略评估
    print("\n8. 策略评估:")
    if performance['total_return'] > 0:
        print("   ✓ 策略产生正收益")
    else:
        print("   ✗ 策略产生负收益")
    
    # 计算基准收益（买入持有）
    buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
    print(f"   买入持有收益率: {buy_hold_return:.2%}")
    print(f"   策略超额收益: {performance['total_return'] - buy_hold_return:.2%}")
    
    if performance['total_return'] > buy_hold_return:
        print("   ✓ 策略跑赢买入持有")
    else:
        print("   ✗ 策略跑输买入持有")

if __name__ == "__main__":
    main()
