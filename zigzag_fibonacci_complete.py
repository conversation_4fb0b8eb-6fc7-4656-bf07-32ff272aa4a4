#!/usr/bin/env python3
"""
ZigZag Fibonacci 策略完整示例

这个脚本演示了一个完整的基于ZigZag和Fibonacci回撤的交易策略
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData, TradingSignal
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator

def create_trending_stock_data():
    """创建包含明显趋势和回撤的股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 设计一个包含多个趋势周期的价格序列
    price_segments = [
        # 第一段：上涨趋势 + Fibonacci回撤
        [100, 102, 105, 108, 112, 115, 118, 120],  # 上涨20%
        [118, 115, 112, 109],                       # 回撤到109 (约50%回撤)
        [112, 115, 118, 122, 125, 128, 130],       # 继续上涨到130
        
        # 第二段：更大的回撤
        [128, 125, 122, 118, 115, 112],            # 回撤到112 (约61.8%回撤)
        [115, 118, 122, 125, 128, 132, 135],       # 反弹到135
        
        # 第三段：下跌趋势
        [133, 130, 127, 124, 120, 116, 112, 108], # 下跌20%
        [110, 113, 116, 114, 111],                 # 反弹后继续下跌
        [108, 105, 102, 99, 96, 93, 90],          # 继续下跌
        
        # 第四段：底部反弹
        [92, 95, 98, 102, 105, 108, 112, 115],    # 从底部反弹
        [113, 110, 107, 110, 113, 116, 120, 124]  # 新的上涨趋势
    ]
    
    # 将所有价格段合并
    all_prices = []
    for segment in price_segments:
        all_prices.extend(segment)
    
    stock_data = []
    for i, close_price in enumerate(all_prices):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        if i == 0:
            open_price = close_price
        else:
            prev_close = all_prices[i-1]
            # 开盘价接近前一日收盘价，但有小幅跳空
            gap = (close_price - prev_close) * 0.2
            open_price = prev_close + gap
        
        # 最高价和最低价
        high_price = max(open_price, close_price) + abs(close_price * 0.01)
        low_price = min(open_price, close_price) - abs(close_price * 0.01)
        
        # 成交量（价格波动大时成交量增加）
        if i > 0:
            price_change_pct = abs(close_price - all_prices[i-1]) / all_prices[i-1]
            volume = int(1000000 * (1 + price_change_pct * 5))
        else:
            volume = 1000000
        
        stock_data.append(StockData(
            symbol="FIBONACCI_TEST",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(round(close_price, 2))),
            volume=volume
        ))
    
    return stock_data

def simple_backtest(stock_data, signals):
    """简单的回测实现"""
    initial_capital = Decimal('100000')
    capital = initial_capital
    position = 0
    trades = []
    
    for signal in signals:
        if signal.signal_type == 'BUY' and capital > 0:
            # 买入：使用所有可用资金
            shares = int(capital / signal.price)
            if shares > 0:
                cost = shares * signal.price
                capital -= cost
                position += shares
                trades.append({
                    'date': signal.date,
                    'type': 'BUY',
                    'shares': shares,
                    'price': signal.price,
                    'cost': cost,
                    'reason': signal.reason
                })
        
        elif signal.signal_type == 'SELL' and position > 0:
            # 卖出：卖出所有持仓
            proceeds = position * signal.price
            capital += proceeds
            trades.append({
                'date': signal.date,
                'type': 'SELL',
                'shares': position,
                'price': signal.price,
                'proceeds': proceeds,
                'reason': signal.reason
            })
            position = 0
    
    # 计算最终价值
    final_price = stock_data[-1].close
    final_value = capital + position * final_price
    
    return {
        'initial_capital': initial_capital,
        'final_value': final_value,
        'total_return': (final_value - initial_capital) / initial_capital,
        'trades': trades,
        'final_position': position,
        'final_cash': capital
    }

def main():
    """主函数"""
    print("=== ZigZag Fibonacci 策略完整示例 ===\n")
    
    # 1. 创建股票数据
    print("1. 创建股票数据...")
    stock_data = create_trending_stock_data()
    print(f"   创建了 {len(stock_data)} 个交易日数据")
    print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
    
    # 2. 初始化策略
    print("\n2. 初始化 ZigZag Fibonacci 策略...")
    strategy = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5%阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
    )
    
    # 3. 分析ZigZag点
    print("\n3. 分析 ZigZag 转折点...")
    zigzag_points = strategy._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个转折点")
    
    if zigzag_points:
        print("   主要转折点:")
        for i, (idx, price, point_type) in enumerate(zigzag_points[:10]):
            date = stock_data[idx].date
            print(f"   {i+1:2d}. {date.strftime('%m-%d')} ¥{float(price):6.2f} ({point_type})")
    
    # 4. 生成交易信号
    print("\n4. 生成交易信号...")
    signals = strategy.generate_signals(stock_data)
    print(f"   生成了 {len(signals)} 个交易信号")
    
    if signals:
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        print(f"   - 买入信号: {len(buy_signals)}")
        print(f"   - 卖出信号: {len(sell_signals)}")
        
        print("\n   信号详情:")
        for i, signal in enumerate(signals):
            print(f"   {i+1:2d}. {signal.date.strftime('%m-%d')} {signal.signal_type:4s} "
                  f"¥{float(signal.price):6.2f} - {signal.reason}")
    
    # 5. 运行回测
    print("\n5. 运行回测...")
    results = simple_backtest(stock_data, signals)
    
    print(f"   初始资金: ¥{float(results['initial_capital']):,.2f}")
    print(f"   最终价值: ¥{float(results['final_value']):,.2f}")
    print(f"   总收益率: {results['total_return']:.2%}")
    print(f"   交易次数: {len(results['trades'])}")
    
    # 6. 交易分析
    if results['trades']:
        print("\n6. 交易分析:")
        print("   交易记录:")
        for i, trade in enumerate(results['trades']):
            if trade['type'] == 'BUY':
                print(f"   {i+1:2d}. {trade['date'].strftime('%m-%d')} 买入 {trade['shares']:,}股 "
                      f"@ ¥{float(trade['price']):.2f} (成本: ¥{float(trade['cost']):,.2f})")
            else:
                print(f"   {i+1:2d}. {trade['date'].strftime('%m-%d')} 卖出 {trade['shares']:,}股 "
                      f"@ ¥{float(trade['price']):.2f} (收入: ¥{float(trade['proceeds']):,.2f})")
        
        # 计算盈亏
        if len(results['trades']) >= 2:
            print("\n   盈亏分析:")
            for i in range(0, len(results['trades']), 2):
                if i + 1 < len(results['trades']):
                    buy_trade = results['trades'][i]
                    sell_trade = results['trades'][i + 1]
                    if buy_trade['type'] == 'BUY' and sell_trade['type'] == 'SELL':
                        profit = float(sell_trade['proceeds']) - float(buy_trade['cost'])
                        profit_pct = profit / float(buy_trade['cost']) * 100
                        print(f"   交易 {i//2 + 1}: ¥{profit:,.2f} ({profit_pct:+.1f}%)")
    
    # 7. 基准比较
    print("\n7. 基准比较:")
    buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
    print(f"   买入持有收益率: {buy_hold_return:.2%}")
    print(f"   策略收益率: {results['total_return']:.2%}")
    excess_return = float(results['total_return']) - buy_hold_return
    print(f"   超额收益: {excess_return:.2%}")
    
    if float(results['total_return']) > buy_hold_return:
        print("   ✓ 策略跑赢买入持有")
    else:
        print("   ✗ 策略跑输买入持有")
    
    print(f"\n策略总结:")
    print(f"- 使用ZigZag阈值: {strategy.zigzag_threshold}")
    print(f"- Fibonacci回撤水平: {strategy.fib_levels}")
    print(f"- 识别转折点: {len(zigzag_points)}个")
    print(f"- 生成信号: {len(signals)}个")
    print(f"- 执行交易: {len(results['trades'])}次")
    print(f"- 最终收益率: {results['total_return']:.2%}")

if __name__ == "__main__":
    main()
