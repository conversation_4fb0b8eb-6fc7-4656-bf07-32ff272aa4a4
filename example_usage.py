from datetime import datetime, timedelta
from decimal import Decimal
from stock_backtest import BacktestEngine, StockData, get_stock_data, dataframe_to_stock_data, StockChartVisualizer

# 示例用法
def create_sample_data():
    """创建示例数据 - 包含上涨下跌波动"""
    sample_data = []
    base_price = Decimal('100')
    start_date = datetime(2024, 1, 1)
    
    for i in range(100):
        date = start_date + timedelta(days=i)
        
        # 创建波动模式：先下跌再上涨，产生买卖信号
        if i < 30:
            # 前30天下跌趋势
            price = base_price - Decimal(str(i * 0.8)) + Decimal(str((i % 5) * 1.5))
        elif i < 60:
            # 中间30天上涨趋势  
            price = base_price - Decimal('24') + Decimal(str((i-30) * 1.2)) + Decimal(str((i % 7) * 2))
        else:
            # 后40天震荡
            price = base_price + Decimal('12') + Decimal(str((i % 10) * 3)) - Decimal(str((i % 15) * 2))
        
        stock_data = StockData(
            symbol='TEST',
            date=date,
            open=price - Decimal('1'),
            high=price + Decimal('2'),
            low=price - Decimal('2'),
            close=price,
            volume=1000000
        )
        sample_data.append(stock_data)
    
    return sample_data

def create_real_data():
    """获取真实股票数据"""
    try:
        # 获取平安银行近6个月数据
        df = get_stock_data("003021", "20240101", "20241201")
        stock_data = dataframe_to_stock_data(df, "003021")
        return stock_data
    except Exception as e:
        print(f"获取真实数据失败: {e}")
        return create_sample_data()  # fallback到示例数据

def main():
    # 创建回测引擎
    engine = BacktestEngine(initial_capital=100000, commission_rate=0.001)
    
    # 准备数据
    test_data = create_real_data()
    stock_data_dict = {'000001': test_data}
    
    # 运行回测
    results = engine.run_backtest(stock_data_dict)
    
    # 创建可视化
    visualizer = StockChartVisualizer()
    
    # 输出结果并生成图表
    for symbol, result in results.items():
        print(f"\n=== {symbol} 回测结果 ===")
        perf = result['performance']
        print(f"初始资金: {perf['initial_capital']}")
        print(f"最终价值: {perf['final_value']}")
        print(f"总收益率: {perf['total_return']:.2%}")
        print(f"交易次数: {len(perf['trades'])}")
        
        # 生成图表
        chart = visualizer.create_kline_chart(
            result['data'], 
            result['signals'], 
            perf['trades'],
            symbol
        )
        
        # 保存图表
        filename = f"{symbol}_analysis.html"
        visualizer.save_chart(chart, filename)
        
        # 显示摘要
        summary = visualizer.create_performance_summary(result)
        print(summary)

if __name__ == "__main__":
    main()





