from typing import List, Dict, Any
from pyecharts import options as opts
from pyecharts.charts import K<PERSON>, Line, Scatter, Grid
from pyecharts.commons.utils import JsCode
from .data_models import StockData, TradingSignal

class StockChartVisualizer:
    """股票图表可视化器"""
    
    def __init__(self):
        self.colors = {
            'buy': '#00da3c',
            'sell': '#ec0000',
            'macd': '#2f4554',
            'signal': '#c23531',
            'bb_upper': '#61a0a8',
            'bb_middle': '#d48265',
            'bb_lower': '#91c7ae'
        }
    
    def create_kline_chart(self, stock_data: List[StockData], signals: List[TradingSignal],
                          trades: List[Dict], symbol: str, zigzag_points: List = None) -> Grid:
        """创建K线图表"""
        # 准备K线数据
        kline_data = []
        dates = []
        volumes = []
        macd_data = []
        signal_data = []
        histogram_data = []
        bb_upper = []
        bb_middle = []
        bb_lower = []
        net_value = []  # 净值曲线
        
        # 计算净值曲线
        current_capital = float(trades[0]['cost']) if trades else 100000
        position = 0
        trade_idx = 0
        
        for i, data in enumerate(stock_data):
            dates.append(data.date.strftime('%Y-%m-%d'))
            kline_data.append([
                float(data.open),
                float(data.close), 
                float(data.low),
                float(data.high)
            ])
            volumes.append([len(dates)-1, float(data.volume), 1 if data.close > data.open else -1])
            
            # 技术指标数据
            macd_data.append(float(data.macd) if data.macd else None)
            signal_data.append(float(data.macd_signal) if data.macd_signal else None)
            histogram_data.append(float(data.macd_histogram) if data.macd_histogram else None)
            bb_upper.append(float(data.bb_upper) if data.bb_upper else None)
            bb_middle.append(float(data.bb_middle) if data.bb_middle else None)
            bb_lower.append(float(data.bb_lower) if data.bb_lower else None)
            
            # 计算当日净值
            if trade_idx < len(trades):
                trade = trades[trade_idx]
                if data.date.date() == trade['date'].date():
                    if trade['type'] == 'BUY':
                        current_capital -= float(trade['cost'])
                        position = trade['shares']
                    elif trade['type'] == 'SELL':
                        current_capital += float(trade['proceeds'])
                        position = 0
                    trade_idx += 1
            
            # 净值 = 现金 + 持仓市值
            current_value = current_capital + position * float(data.close)
            net_value.append(current_value)
        
        # 不创建交易区间图形（去掉矩形框）
        trade_graphics = []
        
        # 创建K线图
        kline = (
            Kline()
            .add_xaxis(dates)
            .add_yaxis(
                series_name="",
                y_axis=kline_data,
                itemstyle_opts=opts.ItemStyleOpts(
                    color="#00da3c",
                    color0="#ec0000",
                    border_color="#00da3c",
                    border_color0="#ec0000",
                ),
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title=f"{symbol} 股票回测分析"),
                xaxis_opts=opts.AxisOpts(is_scale=True, axispointer_opts=opts.AxisPointerOpts(is_show=True, link=[{"xAxisIndex": "all"}])),
                yaxis_opts=opts.AxisOpts(is_scale=True, splitline_opts=opts.SplitLineOpts(is_show=True)),
                tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
                datazoom_opts=[
                    opts.DataZoomOpts(is_show=False, type_="inside", xaxis_index=[0, 1, 2, 3]),
                    opts.DataZoomOpts(is_show=True, xaxis_index=[0, 1, 2, 3], pos_top="97%"),
                ],
                legend_opts=opts.LegendOpts(is_show=True, pos_top="1%"),
                axispointer_opts=opts.AxisPointerOpts(
                    is_show=True,
                    link=[{"xAxisIndex": "all"}],
                    linestyle_opts=opts.LineStyleOpts(type_="dashed", width=1)
                ),
                graphic_opts=trade_graphics
            )
        )
        
        # 添加布林线（9天平均线，1倍标准差）
        bb_line = (
            Line()
            .add_xaxis(dates)
            .add_yaxis("布林上轨(9,1)", bb_upper, is_smooth=True,
                      linestyle_opts=opts.LineStyleOpts(color="#FF9999", width=1.5, type_="dashed"),
                      label_opts=opts.LabelOpts(is_show=False),
                      symbol="none")
            .add_yaxis("布林中轨(9日均线)", bb_middle, is_smooth=True,
                      linestyle_opts=opts.LineStyleOpts(color="#4169E1", width=2),
                      label_opts=opts.LabelOpts(is_show=False),
                      symbol="none")
            .add_yaxis("布林下轨(9,1)", bb_lower, is_smooth=True,
                      linestyle_opts=opts.LineStyleOpts(color="#99FF99", width=1.5, type_="dashed"),
                      label_opts=opts.LabelOpts(is_show=False),
                      symbol="none")
            .set_global_opts(legend_opts=opts.LegendOpts(is_show=True, pos_top="5%"))
        )

        # 创建ZigZag线和标注点（如果提供了ZigZag点）
        zigzag_line = None
        zigzag_scatter = None
        if zigzag_points:
            # 创建ZigZag连线数据
            zigzag_data = [None] * len(dates)
            high_points = [None] * len(dates)
            low_points = [None] * len(dates)

            for idx, price, point_type in zigzag_points:
                if idx < len(dates):
                    zigzag_data[idx] = float(price)
                    if point_type == 'HIGH':
                        high_points[idx] = float(price)
                    else:  # LOW
                        low_points[idx] = float(price)

            # 创建ZigZag连线
            zigzag_line = (
                Line()
                .add_xaxis(dates)
                .add_yaxis("ZigZag线", zigzag_data,
                          is_connect_nones=False,
                          linestyle_opts=opts.LineStyleOpts(color="#FF6B6B", width=2, type_="dashed"),
                          label_opts=opts.LabelOpts(is_show=False),
                          symbol="none")  # 不显示线上的符号，用散点图单独显示
                .set_global_opts(legend_opts=opts.LegendOpts(is_show=False))
            )

            # 创建ZigZag标注点（高点和低点用不同颜色和形状）
            zigzag_scatter = (
                Scatter()
                .add_xaxis(dates)
                .add_yaxis("ZigZag高点", high_points,
                          symbol="triangle", symbol_size=8,
                          itemstyle_opts=opts.ItemStyleOpts(color="#FF4444", border_color="#CC0000", border_width=2),
                          label_opts=opts.LabelOpts(is_show=False))  # 不显示价格标签
                .add_yaxis("ZigZag低点", low_points,
                          symbol="triangle", symbol_size=8, symbol_rotate=180,
                          itemstyle_opts=opts.ItemStyleOpts(color="#44FF44", border_color="#00CC00", border_width=2),
                          label_opts=opts.LabelOpts(is_show=False))  # 不显示价格标签
                .set_global_opts(legend_opts=opts.LegendOpts(is_show=True, pos_top="4%"))
            )

        # 创建成交量图
        volume_bar = (
            Line()
            .add_xaxis(dates)
            .add_yaxis("成交量", [v[1] for v in volumes], 
                      areastyle_opts=opts.AreaStyleOpts(opacity=0.5),
                      label_opts=opts.LabelOpts(is_show=False))
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(is_scale=True, axispointer_opts=opts.AxisPointerOpts(is_show=True)),
                yaxis_opts=opts.AxisOpts(is_scale=True),
                legend_opts=opts.LegendOpts(is_show=False),
            )
        )
        
        # 创建MACD图 - 减小符号大小
        macd_line = (
            Line()
            .add_xaxis(dates)
            .add_yaxis("MACD", macd_data, is_smooth=True,
                      linestyle_opts=opts.LineStyleOpts(color=self.colors['macd']),
                      label_opts=opts.LabelOpts(is_show=False),
                      symbol_size=2)
            .add_yaxis("Signal", signal_data, is_smooth=True,
                      linestyle_opts=opts.LineStyleOpts(color=self.colors['signal']),
                      label_opts=opts.LabelOpts(is_show=False),
                      symbol_size=2)
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(is_scale=True, axispointer_opts=opts.AxisPointerOpts(is_show=True)),
                yaxis_opts=opts.AxisOpts(is_scale=True),
                legend_opts=opts.LegendOpts(is_show=False),
            )
        )
        
        # 创建净值曲线 - 减小符号大小
        net_value_line = (
            Line()
            .add_xaxis(dates)
            .add_yaxis("净值", net_value, is_smooth=True,
                      linestyle_opts=opts.LineStyleOpts(color="#ff7f0e", width=2),
                      label_opts=opts.LabelOpts(is_show=False),
                      symbol_size=2)
            .set_global_opts(
                xaxis_opts=opts.AxisOpts(is_scale=True, axispointer_opts=opts.AxisPointerOpts(is_show=True)),
                yaxis_opts=opts.AxisOpts(is_scale=True),
                legend_opts=opts.LegendOpts(is_show=False),
            )
        )
        
        # 添加交易标记点
        buy_points = []
        sell_points = []
        
        # 为每个日期创建交易标记
        for date in dates:
            buy_point = None
            sell_point = None
            
            # 检查是否有交易
            for trade in trades:
                trade_date = trade['date'].strftime('%Y-%m-%d')
                if trade_date == date:
                    if trade['type'] == 'BUY':
                        buy_point = float(trade['price'])
                    elif trade['type'] == 'SELL':
                        sell_point = float(trade['price'])
            
            buy_points.append(buy_point)
            sell_points.append(sell_point)
        
        # 添加交易标记散点
        trade_scatter = (
            Scatter()
            .add_xaxis(dates)
            .add_yaxis("买入", buy_points, symbol="diamond", symbol_size=10,
                      itemstyle_opts=opts.ItemStyleOpts(color="#0066FF", border_color="#0033CC", border_width=2))
            .add_yaxis("卖出", sell_points, symbol="rect", symbol_size=10,
                      itemstyle_opts=opts.ItemStyleOpts(color="#FF6600", border_color="#CC3300", border_width=2))
            .set_global_opts(legend_opts=opts.LegendOpts(is_show=True, pos_top="3%"))
        )
        
        # 组合图表
        kline_overlap = kline.overlap(bb_line).overlap(trade_scatter)

        # 如果有ZigZag线和标注点，添加到主图中
        if zigzag_line:
            kline_overlap = kline_overlap.overlap(zigzag_line)
        if zigzag_scatter:
            kline_overlap = kline_overlap.overlap(zigzag_scatter)
        
        grid = (
            Grid(init_opts=opts.InitOpts(width="1400px", height="1000px"))
            .add(kline_overlap, grid_opts=opts.GridOpts(pos_left="3%", pos_right="1%", height="45%"))
            .add(volume_bar, grid_opts=opts.GridOpts(pos_left="3%", pos_right="1%", pos_top="50%", height="15%"))
            .add(macd_line, grid_opts=opts.GridOpts(pos_left="3%", pos_right="1%", pos_top="67%", height="15%"))
            .add(net_value_line, grid_opts=opts.GridOpts(pos_left="3%", pos_right="1%", pos_top="84%", height="13%"))
        )
        
        return grid
    
    def _create_trade_areas_graphic(self, trades: List[Dict], dates: List[str]) -> List[dict]:
        """创建交易区间矩形 - 使用graphic组件"""
        if not trades:
            return []
        
        buy_trades = [t for t in trades if t['type'] == 'BUY']
        sell_trades = [t for t in trades if t['type'] == 'SELL']
        
        if not buy_trades or not sell_trades:
            return []
        
        graphic_items = []
        
        for i, buy in enumerate(buy_trades):
            if i < len(sell_trades):
                sell = sell_trades[i]
                buy_date = buy['date'].strftime('%Y-%m-%d')
                sell_date = sell['date'].strftime('%Y-%m-%d')
                
                if buy_date in dates and sell_date in dates:
                    buy_idx = dates.index(buy_date)
                    sell_idx = dates.index(sell_date)
                    buy_price = float(buy['price'])
                    sell_price = float(sell['price'])
                    
                    # 盈利绿色，亏损红色
                    if sell_price > buy_price:
                        fill_color = "rgba(0, 218, 60, 0.15)"
                        stroke_color = "rgba(0, 218, 60, 0.4)"
                    else:
                        fill_color = "rgba(236, 0, 0, 0.15)"
                        stroke_color = "rgba(236, 0, 0, 0.4)"
                    
                    graphic_item = {
                        "type": "rect",
                        "position": [buy_idx, min(buy_price, sell_price)],
                        "shape": {
                            "width": abs(sell_idx - buy_idx),
                            "height": abs(sell_price - buy_price)
                        },
                        "style": {
                            "fill": fill_color,
                            "stroke": stroke_color,
                            "lineWidth": 1
                        },
                        "z": 1
                    }
                    graphic_items.append(graphic_item)
        
        return graphic_items
    
    def save_chart(self, grid: Grid, filename: str = "stock_analysis.html"):
        """保存图表到HTML文件"""
        grid.render(filename)
        print(f"图表已保存到: {filename}")
    
    def save_chart_with_summary(self, grid: Grid, results: Dict[str, Any], filename: str = "stock_analysis.html"):
        """保存图表和业绩摘要到HTML文件"""
        # 生成图表HTML
        grid.render(filename)
        
        # 读取生成的HTML
        with open(filename, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 生成业绩摘要
        summary_html = self.create_performance_summary(results)
        
        # 在图表下方插入摘要
        insert_pos = html_content.find('</body>')
        if insert_pos != -1:
            new_html = html_content[:insert_pos] + summary_html + html_content[insert_pos:]
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(new_html)
        
        print(f"图表和摘要已保存到: {filename}")
        
    def create_performance_summary(self, results: Dict[str, Any]) -> str:
        """创建业绩摘要HTML"""
        perf = results['performance']
        signals = results['signals']
        
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        
        html = f"""
        <div style="margin: 20px; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>回测结果摘要</h3>
            <p><strong>初始资金:</strong> ¥{perf['initial_capital']:,.2f}</p>
            <p><strong>最终价值:</strong> ¥{perf['final_value']:,.2f}</p>
            <p><strong>总收益率:</strong> {perf['total_return']:.2%}</p>
            <p><strong>交易次数:</strong> {len(perf['trades'])}</p>
            <p><strong>信号统计:</strong> 买入{len(buy_signals)}次, 卖出{len(sell_signals)}次</p>
        </div>
        """
        return html






























