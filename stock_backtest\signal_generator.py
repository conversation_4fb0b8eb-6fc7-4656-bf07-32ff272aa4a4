from typing import List, Optional, Tuple
from .data_models import StockData, TradingSignal
from decimal import Decimal
import numpy as np

class SignalGenerator:
    """交易信号生成器"""
    
    def __init__(self, macd_divergence_threshold=0.1, bb_reversal_threshold=0.05):
        self.macd_divergence_threshold = Decimal(str(macd_divergence_threshold))
        self.bb_reversal_threshold = Decimal(str(bb_reversal_threshold))
    
    def generate_signals(self, stock_data: List[StockData]) -> List[TradingSignal]:
        """生成交易信号"""
        signals = []
        
        for i in range(2, len(stock_data)):
            current = stock_data[i]
            prev = stock_data[i-1]
            prev2 = stock_data[i-2]
            
            # MACD背离信号
            macd_signal = self._check_macd_divergence(current, prev, prev2)
            if macd_signal:
                signals.append(macd_signal)
            
            # 布林线折返信号
            bb_signal = self._check_bollinger_reversal(current, prev)
            if bb_signal:
                signals.append(bb_signal)
        
        return signals
    
    def _check_macd_divergence(self, current: StockData, prev: StockData, prev2: StockData) -> Optional[TradingSignal]:
        """检查MACD背离"""
        if not all([current.macd, prev.macd, prev2.macd, 
                   current.macd_histogram, prev.macd_histogram]):
            return None
        
        # 价格创新高，MACD未创新高（顶背离）
        if (current.close > prev.close > prev2.close and 
            current.macd < prev.macd and 
            current.macd_histogram < prev.macd_histogram):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='SELL',
                price=current.close,
                reason='MACD顶背离',
                confidence=0.8
            )
        
        # 价格创新低，MACD未创新低（底背离）
        if (current.close < prev.close < prev2.close and 
            current.macd > prev.macd and 
            current.macd_histogram > prev.macd_histogram):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='BUY',
                price=current.close,
                reason='MACD底背离',
                confidence=0.8
            )
        
        return None
    
    def _check_bollinger_reversal(self, current: StockData, prev: StockData) -> Optional[TradingSignal]:
        """检查布林线折返"""
        if not all([current.bb_upper, current.bb_lower, current.close, prev.close]):
            return None
        
        # 价格触及上轨后回落
        if (prev.close >= current.bb_upper * (Decimal(1) - self.bb_reversal_threshold) and 
            current.close < prev.close):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='SELL',
                price=current.close,
                reason='布林上轨折返',
                confidence=0.7
            )
        
        # 价格触及下轨后反弹
        if (prev.close <= current.bb_lower * (Decimal(1) + self.bb_reversal_threshold) and 
            current.close > prev.close):
            return TradingSignal(
                symbol=current.symbol,
                date=current.date,
                signal_type='BUY',
                price=current.close,
                reason='布林下轨折返',
                confidence=0.7
            )
        
        return None


class ZigZagFibonacciSignalGenerator:
    """基于ZigZag和Fibonacci回撤的交易信号生成器"""

    def __init__(self, zigzag_threshold=0.05, fib_levels=None):
        """
        初始化ZigZag Fibonacci信号生成器

        Args:
            zigzag_threshold: ZigZag的最小变化阈值（百分比）
            fib_levels: Fibonacci回撤水平列表
        """
        self.zigzag_threshold = Decimal(str(zigzag_threshold))
        self.fib_levels = fib_levels or [0.236, 0.382, 0.5, 0.618, 0.786]

    def generate_signals(self, stock_data: List[StockData]) -> List[TradingSignal]:
        """生成基于ZigZag和Fibonacci回撤的交易信号"""
        if len(stock_data) < 10:  # 需要足够的数据点
            return []

        # 计算ZigZag点
        zigzag_points = self._calculate_zigzag(stock_data)

        if len(zigzag_points) < 3:  # 需要至少3个ZigZag点来计算Fibonacci
            return []

        signals = []

        # 遍历股票数据，寻找Fibonacci回撤信号
        for i in range(len(stock_data)):
            current = stock_data[i]

            # 获取当前点之前的最近两个ZigZag点
            recent_zigzag = self._get_recent_zigzag_points(zigzag_points, i, 2)

            if len(recent_zigzag) >= 2:
                signal = self._check_fibonacci_signal(current, recent_zigzag, i)
                if signal:
                    signals.append(signal)

        return signals

    def _calculate_zigzag(self, stock_data: List[StockData]) -> List[Tuple[int, Decimal, str]]:
        """
        计算ZigZag点

        Returns:
            List of (index, price, type) where type is 'HIGH' or 'LOW'
        """
        zigzag_points = []

        if len(stock_data) < 3:
            return zigzag_points

        # 寻找第一个有效的高点或低点
        current_trend = None
        last_extreme_idx = 0
        last_extreme_price = stock_data[0].close

        for i in range(1, len(stock_data)):
            current_price = stock_data[i].close

            # 计算价格变化百分比
            price_change = abs(current_price - last_extreme_price) / last_extreme_price

            if price_change >= self.zigzag_threshold:
                if current_price > last_extreme_price:
                    # 价格上涨
                    if current_trend != 'UP':
                        # 趋势转为上涨，前一个点是低点
                        if current_trend is not None:
                            zigzag_points.append((last_extreme_idx, last_extreme_price, 'LOW'))
                        current_trend = 'UP'
                        last_extreme_idx = i
                        last_extreme_price = current_price
                    else:
                        # 继续上涨，更新高点
                        if current_price > last_extreme_price:
                            last_extreme_idx = i
                            last_extreme_price = current_price

                else:
                    # 价格下跌
                    if current_trend != 'DOWN':
                        # 趋势转为下跌，前一个点是高点
                        if current_trend is not None:
                            zigzag_points.append((last_extreme_idx, last_extreme_price, 'HIGH'))
                        current_trend = 'DOWN'
                        last_extreme_idx = i
                        last_extreme_price = current_price
                    else:
                        # 继续下跌，更新低点
                        if current_price < last_extreme_price:
                            last_extreme_idx = i
                            last_extreme_price = current_price

            # 更新当前极值点（即使没有达到阈值）
            if current_trend == 'UP' and current_price > last_extreme_price:
                last_extreme_idx = i
                last_extreme_price = current_price
            elif current_trend == 'DOWN' and current_price < last_extreme_price:
                last_extreme_idx = i
                last_extreme_price = current_price

        # 添加最后一个极值点
        if current_trend is not None:
            point_type = 'HIGH' if current_trend == 'UP' else 'LOW'
            zigzag_points.append((last_extreme_idx, last_extreme_price, point_type))

        return zigzag_points

    def _get_recent_zigzag_points(self, zigzag_points: List[Tuple[int, Decimal, str]],
                                 current_idx: int, count: int) -> List[Tuple[int, Decimal, str]]:
        """获取当前索引之前的最近N个ZigZag点"""
        recent_points = []

        for point in reversed(zigzag_points):
            if point[0] < current_idx:
                recent_points.append(point)
                if len(recent_points) >= count:
                    break

        return list(reversed(recent_points))

    def _check_fibonacci_signal(self, current: StockData, zigzag_points: List[Tuple[int, Decimal, str]],
                               current_idx: int) -> Optional[TradingSignal]:
        """检查Fibonacci回撤信号"""
        if len(zigzag_points) < 2:
            return None

        # 获取最近的两个ZigZag点
        point1 = zigzag_points[-2]  # 较早的点
        point2 = zigzag_points[-1]  # 较近的点

        # 判断是上涨还是下跌趋势
        if point2[1] > point1[1]:  # 上涨趋势（从低点到高点）
            # 寻找回撤买入机会：从高点回撤到Fibonacci水平
            high_price = point2[1]  # 高点
            low_price = point1[1]   # 低点

            for fib_level in self.fib_levels:
                # Fibonacci回撤价格 = 高点 - (高点-低点) * 回撤比例
                fib_price = high_price - (high_price - low_price) * Decimal(str(fib_level))

                # 检查当前价格是否接近Fibonacci回撤水平
                if self._is_near_price(current.close, fib_price, tolerance=0.03):
                    # 检查是否有反弹迹象
                    if self._has_bullish_momentum(current):
                        return TradingSignal(
                            symbol=current.symbol,
                            date=current.date,
                            signal_type='BUY',
                            price=current.close,
                            reason=f'Fibonacci {fib_level:.1%} 回撤买入',
                            confidence=0.7 + (0.618 - abs(0.618 - fib_level)) * 0.3
                        )

        else:  # 下跌趋势（从高点到低点）
            # 寻找反弹卖出机会：从低点反弹到Fibonacci水平
            high_price = point1[1]  # 高点
            low_price = point2[1]   # 低点

            for fib_level in self.fib_levels:
                # Fibonacci反弹价格 = 低点 + (高点-低点) * 反弹比例
                fib_price = low_price + (high_price - low_price) * Decimal(str(fib_level))

                # 检查当前价格是否接近Fibonacci反弹水平
                if self._is_near_price(current.close, fib_price, tolerance=0.03):
                    # 检查是否有下跌迹象
                    if self._has_bearish_momentum(current):
                        return TradingSignal(
                            symbol=current.symbol,
                            date=current.date,
                            signal_type='SELL',
                            price=current.close,
                            reason=f'Fibonacci {fib_level:.1%} 反弹卖出',
                            confidence=0.7 + (0.618 - abs(0.618 - fib_level)) * 0.3
                        )

        return None

    def _is_near_price(self, current_price: Decimal, target_price: Decimal, tolerance: float = 0.02) -> bool:
        """检查当前价格是否接近目标价格"""
        diff_pct = abs(current_price - target_price) / target_price
        return diff_pct <= Decimal(str(tolerance))

    def _has_bullish_momentum(self, current: StockData) -> bool:
        """检查是否有看涨动量（简化版本）"""
        # 简单检查：收盘价高于开盘价
        return current.close > current.open

    def _has_bearish_momentum(self, current: StockData) -> bool:
        """检查是否有看跌动量（简化版本）"""
        # 简单检查：收盘价低于开盘价
        return current.close < current.open