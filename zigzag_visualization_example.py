#!/usr/bin/env python3
"""
ZigZag可视化使用示例
展示如何在K线图上标注ZigZag转折点
"""

from datetime import datetime, timedelta
from decimal import Decimal
from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer

def create_sample_data():
    """创建示例股票数据"""
    base_date = datetime(2024, 1, 1)
    stock_data = []
    
    # 简单的价格序列，有明显的高低点
    prices = [
        100, 105, 110, 115, 120, 125, 130,  # 上涨趋势
        125, 120, 115, 110, 105, 100, 95,   # 下跌趋势
        100, 105, 110, 115, 120, 125,       # 反弹
        120, 115, 110, 105, 100, 95, 90,    # 再次下跌
        95, 100, 105, 110, 115, 120         # 最终反弹
    ]
    
    for i, price in enumerate(prices):
        date = base_date + timedelta(days=i)
        price_decimal = Decimal(str(price))
        
        # 生成OHLC数据
        open_price = price_decimal
        close_price = price_decimal
        high_price = price_decimal * Decimal('1.02')  # 高2%
        low_price = price_decimal * Decimal('0.98')   # 低2%
        
        stock_data.append(StockData(
            symbol="EXAMPLE",
            date=date,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=100000
        ))
    
    return stock_data

def main():
    """主函数 - 演示ZigZag可视化功能"""
    print("=== ZigZag可视化示例 ===\n")
    
    # 1. 创建示例数据
    print("1. 创建示例数据...")
    stock_data = create_sample_data()
    print(f"   数据点数: {len(stock_data)}")
    print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
    
    # 2. 初始化ZigZag策略
    print("\n2. 初始化ZigZag策略...")
    strategy = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.10,  # 10%阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
    )
    
    # 3. 计算ZigZag转折点
    print("\n3. 计算ZigZag转折点...")
    zigzag_points = strategy._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个转折点:")
    
    for i, (idx, price, point_type) in enumerate(zigzag_points):
        date = stock_data[idx].date
        print(f"   {i+1}. {date.strftime('%Y-%m-%d')} ¥{float(price):6.2f} ({point_type})")
    
    # 4. 生成交易信号
    print("\n4. 生成交易信号...")
    signals = strategy.generate_signals(stock_data)
    print(f"   生成了 {len(signals)} 个交易信号")
    
    # 5. 运行回测（包含止损）
    print("\n5. 运行回测...")
    backtest_engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        signal_generator=strategy,
        stop_loss_pct=0.05  # 5%止损
    )
    
    stock_data_dict = {"EXAMPLE": stock_data}
    results = backtest_engine.run_backtest(stock_data_dict)
    
    example_results = results["EXAMPLE"]
    performance = example_results['performance']
    
    print(f"   初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"   最终价值: ¥{float(performance['final_value']):,.2f}")
    print(f"   总收益率: {performance['total_return']:.2%}")
    print(f"   交易次数: {len(performance['trades'])}")
    print(f"   止损次数: {performance['stop_loss_count']}")
    
    # 6. 创建可视化图表
    print("\n6. 创建可视化图表...")
    visualizer = StockChartVisualizer()
    
    # 创建包含ZigZag标注的图表
    chart = visualizer.create_kline_chart(
        stock_data=stock_data,
        signals=signals,
        trades=performance['trades'],
        symbol="EXAMPLE",
        zigzag_points=zigzag_points  # 传入ZigZag点进行可视化
    )
    
    # 保存图表
    filename = "zigzag_example.html"
    visualizer.save_chart_with_summary(chart, example_results, filename)
    
    print(f"   图表已保存到: {filename}")
    print("\n   图表特性:")
    print("   ✓ K线图显示价格走势")
    print("   ✓ ZigZag连线（红色虚线）连接转折点")
    print("   ✓ 高点标注（红色向上三角形 ▲）")
    print("   ✓ 低点标注（绿色向下三角形 ▼）")
    print("   ✓ 价格标签显示具体数值")
    print("   ✓ 交易信号和止损标记")
    print("   ✓ 成交量、MACD指标和净值曲线")
    
    print(f"\n=== 使用说明 ===")
    print("1. 修改了 _calculate_zigzag 方法，现在使用 high 和 low 价格计算")
    print("2. 添加了止损功能，可在 BacktestEngine 中设置 stop_loss_pct 参数")
    print("3. 改进了可视化效果，ZigZag点用不同颜色和形状标注")
    print("4. 在调用 create_kline_chart 时传入 zigzag_points 参数即可显示标注")
    
    # 打开浏览器查看
    try:
        import webbrowser
        import os
        file_path = os.path.abspath(filename)
        webbrowser.open(f'file://{file_path}')
        print(f"\n正在打开浏览器查看图表...")
    except:
        print(f"\n请手动打开 {filename} 查看图表")

if __name__ == "__main__":
    main()
