#!/usr/bin/env python3
"""
测试改进后的ZigZag可视化功能
"""

from datetime import datetime, timedelta
from decimal import Decimal
from stock_backtest.data_models import StockData, TradingSignal
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest.backtest_engine import BacktestEngine
from stock_backtest.visualization import StockChartVisualizer
import random
import math

def create_zigzag_demo_data():
    """创建用于演示ZigZag可视化的数据"""
    base_date = datetime(2024, 1, 1)
    stock_data = []
    
    # 创建一个有明显ZigZag模式的价格序列
    base_price = 100.0
    days = 60  # 60天数据
    
    # 使用正弦波加上趋势和随机波动来创建有趣的价格模式
    for i in range(days):
        date = base_date + timedelta(days=i)
        
        # 基础趋势（先上涨后下跌再上涨）
        trend_factor = 1.0
        if i < 20:
            trend_factor = 1.0 + (i * 0.02)  # 前20天上涨
        elif i < 40:
            trend_factor = 1.4 - ((i - 20) * 0.025)  # 中间20天下跌
        else:
            trend_factor = 0.9 + ((i - 40) * 0.015)  # 后20天恢复上涨
        
        # 添加周期性波动
        cycle_factor = 1.0 + 0.1 * math.sin(i * 0.3)
        
        # 添加随机波动
        random_factor = 1.0 + random.uniform(-0.05, 0.05)
        
        # 计算当日价格
        price = base_price * trend_factor * cycle_factor * random_factor
        price_decimal = Decimal(str(round(price, 2)))
        
        # 生成OHLC数据
        daily_volatility = price * 0.02  # 2%日内波动
        open_price = price_decimal
        close_price = price_decimal
        high_price = price_decimal + Decimal(str(round(random.uniform(0, daily_volatility), 2)))
        low_price = price_decimal - Decimal(str(round(random.uniform(0, daily_volatility), 2)))
        
        # 确保价格逻辑正确
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        stock_data.append(StockData(
            symbol="ZIGZAG_DEMO",
            date=date,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=random.randint(100000, 1000000)
        ))
    
    return stock_data

def test_zigzag_visualization():
    """测试ZigZag可视化功能"""
    print("=== 测试ZigZag可视化功能 ===\n")
    
    # 1. 创建演示数据
    print("1. 创建演示数据...")
    stock_data = create_zigzag_demo_data()
    print(f"   创建了 {len(stock_data)} 天的股票数据")
    print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
    
    # 2. 初始化ZigZag策略
    print("\n2. 初始化ZigZag策略...")
    strategy = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.08,  # 8%阈值，确保能识别出明显的转折点
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
    )
    print(f"   ZigZag阈值: {float(strategy.zigzag_threshold):.1%}")
    
    # 3. 计算ZigZag点
    print("\n3. 计算ZigZag转折点...")
    zigzag_points = strategy._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个转折点")
    
    if zigzag_points:
        print("   转折点详情:")
        for i, (idx, price, point_type) in enumerate(zigzag_points):
            date = stock_data[idx].date
            print(f"   {i+1:2d}. {date.strftime('%Y-%m-%d')} ¥{float(price):7.2f} ({point_type})")
    
    # 4. 生成交易信号
    print("\n4. 生成交易信号...")
    signals = strategy.generate_signals(stock_data)
    print(f"   生成了 {len(signals)} 个交易信号")
    
    if signals:
        print("   信号详情:")
        for i, signal in enumerate(signals):
            print(f"   {i+1:2d}. {signal.date.strftime('%Y-%m-%d')} {signal.signal_type} "
                  f"¥{float(signal.price):7.2f} - {signal.reason}")
    
    # 5. 运行回测
    print("\n5. 运行回测...")
    backtest_engine = BacktestEngine(
        initial_capital=100000,
        commission_rate=0.001,
        signal_generator=strategy,
        stop_loss_pct=0.05  # 5%止损
    )
    
    stock_data_dict = {"ZIGZAG_DEMO": stock_data}
    results = backtest_engine.run_backtest(stock_data_dict)
    
    demo_results = results["ZIGZAG_DEMO"]
    performance = demo_results['performance']
    
    print(f"   初始资金: ¥{float(performance['initial_capital']):,.2f}")
    print(f"   最终价值: ¥{float(performance['final_value']):,.2f}")
    print(f"   总收益率: {performance['total_return']:.2%}")
    print(f"   交易次数: {len(performance['trades'])}")
    print(f"   止损次数: {performance['stop_loss_count']}")
    
    # 6. 生成可视化图表
    print("\n6. 生成可视化图表...")
    try:
        visualizer = StockChartVisualizer()
        
        # 创建包含改进ZigZag标注的图表
        chart = visualizer.create_kline_chart(
            stock_data=demo_results['data'],
            signals=signals,
            trades=performance['trades'],
            symbol="ZIGZAG_DEMO",
            zigzag_points=zigzag_points
        )
        
        # 保存图表
        filename = "zigzag_visualization_demo.html"
        visualizer.save_chart_with_summary(chart, demo_results, filename)
        print(f"   图表已保存到: {filename}")
        print("   图表包含以下改进的可视化元素:")
        print("   - K线图")
        print("   - ZigZag连线（红色虚线）")
        print("   - 高点标注（红色向上三角形 ▲）")
        print("   - 低点标注（绿色向下三角形 ▼）")
        print("   - 价格标签显示")
        print("   - 交易信号标记")
        print("   - 止损交易标记")
        print("   - 成交量图")
        print("   - MACD指标")
        print("   - 净值曲线")
        
        # 打开浏览器查看图表
        import webbrowser
        import os
        file_path = os.path.abspath(filename)
        print(f"\n   正在打开浏览器查看图表...")
        webbrowser.open(f'file://{file_path}')
        
    except Exception as e:
        print(f"   图表生成失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("ZigZag可视化功能测试")
    print("=" * 50)
    
    test_zigzag_visualization()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("请查看生成的HTML文件以查看改进后的ZigZag可视化效果。")

if __name__ == "__main__":
    main()
