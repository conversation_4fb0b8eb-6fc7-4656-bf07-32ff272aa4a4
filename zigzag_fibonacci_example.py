#!/usr/bin/env python3
"""
ZigZag Fibonacci 策略示例

这个脚本演示如何使用基于ZigZag和Fibonacci回撤的交易策略
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator
from stock_backtest import get_stock_data, dataframe_to_stock_data

def create_sample_data():
    """创建示例股票数据（模拟一个波动的股价）"""
    base_date = datetime(2024, 1, 1)
    
    # 模拟股价数据：包含明显的高低点和回撤
    prices = [
        100, 102, 105, 108, 110, 107, 104, 106, 109, 112,  # 上涨后回撤
        115, 118, 120, 117, 114, 116, 119, 122, 125, 123,  # 继续上涨后回撤
        120, 117, 115, 118, 121, 124, 127, 130, 128, 125,  # 再次上涨后回撤
        122, 119, 116, 113, 110, 112, 115, 118, 116, 113,  # 下跌趋势
        110, 107, 104, 101, 98, 100, 103, 106, 104, 101    # 继续下跌后反弹
    ]
    
    stock_data = []
    for i, close_price in enumerate(prices):
        date = base_date + timedelta(days=i)
        
        # 模拟开盘价、最高价、最低价
        open_price = close_price + ((-1) ** i) * 0.5  # 简单的开盘价模拟
        high_price = max(open_price, close_price) + abs(close_price * 0.01)
        low_price = min(open_price, close_price) - abs(close_price * 0.01)
        
        stock_data.append(StockData(
            symbol="TEST",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        ))
    
    return stock_data

def main():
    """主函数"""
    print("=== ZigZag Fibonacci 策略示例 ===\n")
    
    # 创建示例数据
    print("1. 获取股票数据...")
    df = get_stock_data("003021", "20240101", "20241201")
    stock_data = dataframe_to_stock_data(df, "003021")
    print(f"   创建了 {len(stock_data)} 个数据点")
    
    # 初始化信号生成器
    print("\n2. 初始化 ZigZag Fibonacci 信号生成器...")
    signal_generator = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,  # 5% 的ZigZag阈值
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]  # 标准Fibonacci水平
    )
    
    # 生成交易信号
    print("\n3. 生成交易信号...")
    signals = signal_generator.generate_signals(stock_data)
    print(f"   生成了 {len(signals)} 个交易信号")
    
    # 显示信号详情
    if signals:
        print("\n4. 交易信号详情:")
        print("-" * 80)
        print(f"{'日期':<12} {'类型':<6} {'价格':<8} {'置信度':<8} {'原因'}")
        print("-" * 80)
        
        for signal in signals:
            print(f"{signal.date.strftime('%Y-%m-%d'):<12} "
                  f"{signal.signal_type:<6} "
                  f"{float(signal.price):<8.2f} "
                  f"{signal.confidence:<8.2f} "
                  f"{signal.reason}")
    else:
        print("\n4. 未生成任何交易信号")
    
    # 显示ZigZag点分析
    print("\n5. ZigZag 点分析:")
    zigzag_points = signal_generator._calculate_zigzag(stock_data)
    
    if zigzag_points:
        print("-" * 60)
        print(f"{'索引':<6} {'日期':<12} {'价格':<8} {'类型'}")
        print("-" * 60)
        
        for idx, price, point_type in zigzag_points:
            date = stock_data[idx].date
            print(f"{idx:<6} {date.strftime('%Y-%m-%d'):<12} "
                  f"{float(price):<8.2f} {point_type}")
    
    # 计算一些统计信息
    print(f"\n6. 统计信息:")
    print(f"   - 总数据点: {len(stock_data)}")
    print(f"   - ZigZag点数: {len(zigzag_points)}")
    print(f"   - 交易信号数: {len(signals)}")
    
    if signals:
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        print(f"   - 买入信号: {len(buy_signals)}")
        print(f"   - 卖出信号: {len(sell_signals)}")
        
        avg_confidence = sum(s.confidence for s in signals) / len(signals)
        print(f"   - 平均置信度: {avg_confidence:.2f}")
    
    print(f"\n7. 价格范围:")
    prices = [float(data.close) for data in stock_data]
    print(f"   - 最高价: {max(prices):.2f}")
    print(f"   - 最低价: {min(prices):.2f}")
    print(f"   - 价格波动: {(max(prices) - min(prices)) / min(prices) * 100:.1f}%")

if __name__ == "__main__":
    main()
