#!/usr/bin/env python3
"""
调试Fibonacci信号生成
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator

def create_debug_data():
    """创建用于调试的股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 简单明确的价格模式：上涨-回撤-上涨
    prices = [
        100, 105, 110, 115, 120,  # 上涨到120
        115, 110, 107,            # 回撤到107 (约54%回撤，接近50%)
        110, 115, 120, 125        # 反弹到125
    ]
    
    stock_data = []
    for i, close_price in enumerate(prices):
        date = base_date + timedelta(days=i)
        
        # 简单的OHLC
        open_price = close_price - 0.5 if i % 2 == 0 else close_price + 0.5
        high_price = max(open_price, close_price) + 0.5
        low_price = min(open_price, close_price) - 0.5
        
        stock_data.append(StockData(
            symbol="DEBUG",
            date=date,
            open=Decimal(str(open_price)),
            high=Decimal(str(high_price)),
            low=Decimal(str(low_price)),
            close=Decimal(str(close_price)),
            volume=1000000
        ))
    
    return stock_data

def debug_signal_generation():
    """详细调试信号生成过程"""
    print("=== Fibonacci信号生成调试 ===\n")
    
    # 创建数据
    stock_data = create_debug_data()
    print("股票数据:")
    for i, data in enumerate(stock_data):
        print(f"{i:2d}. {data.date.strftime('%m-%d')} ¥{float(data.close):6.2f}")
    
    # 初始化策略
    strategy = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.05,
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
    )
    
    # 计算ZigZag点
    print(f"\nZigZag点 (阈值: {strategy.zigzag_threshold}):")
    zigzag_points = strategy._calculate_zigzag(stock_data)
    for i, (idx, price, point_type) in enumerate(zigzag_points):
        date = stock_data[idx].date
        print(f"{i+1}. 索引{idx:2d} {date.strftime('%m-%d')} ¥{float(price):6.2f} ({point_type})")
    
    # 手动检查每个数据点的信号生成
    print(f"\n详细信号检查:")
    for i in range(len(stock_data)):
        current = stock_data[i]
        
        # 获取当前点之前的最近两个ZigZag点
        recent_zigzag = strategy._get_recent_zigzag_points(zigzag_points, i, 2)
        
        print(f"\n数据点 {i:2d} ({current.date.strftime('%m-%d')}, ¥{float(current.close):6.2f}):")
        print(f"  最近ZigZag点数: {len(recent_zigzag)}")
        
        if len(recent_zigzag) >= 2:
            point1 = recent_zigzag[-2]  # 较早的点
            point2 = recent_zigzag[-1]  # 较近的点
            
            print(f"  点1: 索引{point1[0]:2d} ¥{float(point1[1]):6.2f} ({point1[2]})")
            print(f"  点2: 索引{point2[0]:2d} ¥{float(point2[1]):6.2f} ({point2[2]})")
            
            # 检查趋势
            if point2[1] > point1[1]:
                print(f"  趋势: 上涨 (从¥{float(point1[1]):.2f}到¥{float(point2[1]):.2f})")
                
                # 计算Fibonacci回撤水平
                high_price = point2[1]
                low_price = point1[1]
                
                print(f"  Fibonacci回撤水平:")
                for fib_level in strategy.fib_levels:
                    fib_price = high_price - (high_price - low_price) * Decimal(str(fib_level))
                    distance = abs(current.close - fib_price) / fib_price
                    near = distance <= Decimal('0.03')
                    
                    print(f"    {fib_level:.1%}: ¥{float(fib_price):6.2f} "
                          f"(距离: {float(distance):.1%}, 接近: {near})")
                    
                    if near:
                        momentum = strategy._has_bullish_momentum(current)
                        print(f"      看涨动量: {momentum} (收盘¥{float(current.close):.2f} vs 开盘¥{float(current.open):.2f})")
                        
            else:
                print(f"  趋势: 下跌 (从¥{float(point1[1]):.2f}到¥{float(point2[1]):.2f})")
                
                # 计算Fibonacci反弹水平
                high_price = point1[1]
                low_price = point2[1]
                
                print(f"  Fibonacci反弹水平:")
                for fib_level in strategy.fib_levels:
                    fib_price = low_price + (high_price - low_price) * Decimal(str(fib_level))
                    distance = abs(current.close - fib_price) / fib_price
                    near = distance <= Decimal('0.03')
                    
                    print(f"    {fib_level:.1%}: ¥{float(fib_price):6.2f} "
                          f"(距离: {float(distance):.1%}, 接近: {near})")
                    
                    if near:
                        momentum = strategy._has_bearish_momentum(current)
                        print(f"      看跌动量: {momentum} (收盘¥{float(current.close):.2f} vs 开盘¥{float(current.open):.2f})")
        
        # 检查是否生成信号
        if len(recent_zigzag) >= 2:
            signal = strategy._check_fibonacci_signal(current, recent_zigzag, i)
            if signal:
                print(f"  ✓ 生成信号: {signal.signal_type} - {signal.reason}")
            else:
                print(f"  ✗ 未生成信号")
    
    # 最终信号生成
    print(f"\n最终信号生成:")
    signals = strategy.generate_signals(stock_data)
    if signals:
        for i, signal in enumerate(signals):
            print(f"{i+1}. {signal.date.strftime('%m-%d')} {signal.signal_type} "
                  f"¥{float(signal.price):.2f} - {signal.reason}")
    else:
        print("未生成任何信号")

if __name__ == "__main__":
    debug_signal_generation()
