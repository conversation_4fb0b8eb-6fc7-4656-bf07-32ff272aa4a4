from typing import List, Dict, Any, Union
from decimal import Decimal
from datetime import datetime
from .data_models import StockData, TradingSignal
from .indicators import TechnicalIndicators
from .signal_generator import SignalGenerator, ZigZagFibonacciSignalGenerator

class BacktestEngine:
    """回测引擎"""

    def __init__(self, initial_capital=100000, commission_rate=0.001,
                 signal_generator: Union[SignalGenerator, ZigZagFibonacciSignalGenerator] = None,
                 stop_loss_pct=0.05):
        self.initial_capital = Decimal(str(initial_capital))
        self.commission_rate = Decimal(str(commission_rate))
        self.signal_generator = signal_generator or SignalGenerator()
        self.stop_loss_pct = Decimal(str(stop_loss_pct))  # 止损百分比，默认5%
        
    def run_backtest(self, stock_data_dict: Dict[str, List[StockData]]) -> Dict[str, Any]:
        """运行回测"""
        results = {}

        for symbol, data in stock_data_dict.items():
            # 根据信号生成器类型决定是否计算技术指标
            if isinstance(self.signal_generator, ZigZagFibonacciSignalGenerator):
                # ZigZag策略不需要技术指标，直接使用原始数据
                processed_data = data
            else:
                # 传统策略需要计算技术指标
                processed_data = self._calculate_indicators(data)

            # 生成交易信号
            signals = self.signal_generator.generate_signals(processed_data)

            # 执行回测
            backtest_result = self._execute_backtest(processed_data, signals)

            # 为ZigZag策略添加额外信息
            result_data = {
                'data': processed_data,
                'signals': signals,
                'performance': backtest_result
            }

            # 如果是ZigZag策略，添加ZigZag点信息
            if isinstance(self.signal_generator, ZigZagFibonacciSignalGenerator):
                zigzag_points = self.signal_generator._calculate_zigzag(processed_data)
                result_data['zigzag_points'] = zigzag_points

            results[symbol] = result_data

        return results
    
    def _calculate_indicators(self, data: List[StockData]) -> List[StockData]:
        """计算技术指标"""
        prices = [item.close for item in data]
        
        # 计算MACD
        macd, signal, histogram = TechnicalIndicators.calculate_macd(prices)
        
        # 计算布林线
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.calculate_bollinger_bands(prices)
        
        # 更新数据
        enhanced_data = []
        for i, item in enumerate(data):
            new_item = StockData(
                symbol=item.symbol,
                date=item.date,
                open=item.open,
                high=item.high,
                low=item.low,
                close=item.close,
                volume=item.volume,
                macd=macd[i],
                macd_signal=signal[i],
                macd_histogram=histogram[i],
                bb_upper=bb_upper[i],
                bb_middle=bb_middle[i],
                bb_lower=bb_lower[i]
            )
            enhanced_data.append(new_item)
        
        return enhanced_data
    
    def _execute_backtest(self, data: List[StockData], signals: List[TradingSignal]) -> Dict[str, Any]:
        """执行回测逻辑，包含止损功能"""
        capital = self.initial_capital
        position = 0  # 持仓数量
        trades = []
        buy_price = None  # 记录买入价格，用于止损计算

        signal_dict = {signal.date: signal for signal in signals}

        for stock_data in data:
            # 检查止损条件（如果有持仓）
            if position > 0 and buy_price is not None:
                # 计算当前价格相对于买入价格的跌幅
                price_decline = (buy_price - stock_data.low) / buy_price

                if price_decline >= self.stop_loss_pct:
                    # 触发止损，按当日最低价卖出
                    stop_loss_price = stock_data.low
                    proceeds = position * stop_loss_price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': stock_data.date,
                        'type': 'STOP_LOSS',
                        'shares': position,
                        'price': stop_loss_price,
                        'proceeds': proceeds,
                        'reason': f'止损卖出 (跌幅: {price_decline:.2%})'
                    })
                    position = 0
                    buy_price = None
                    continue  # 止损后不再处理当日的其他信号

            # 处理交易信号
            if stock_data.date in signal_dict:
                signal = signal_dict[stock_data.date]

                if signal.signal_type == 'BUY' and capital > 0 and position == 0:
                    # 买入 - 只有在没有持仓时才买入
                    max_shares = int(capital / (signal.price * (Decimal(1) + self.commission_rate)))
                    if max_shares > 0:
                        cost = max_shares * signal.price * (Decimal(1) + self.commission_rate)
                        capital -= cost
                        position += max_shares
                        buy_price = signal.price  # 记录买入价格
                        trades.append({
                            'date': signal.date,
                            'type': 'BUY',
                            'shares': max_shares,
                            'price': signal.price,
                            'cost': cost,
                            'reason': signal.reason
                        })

                elif signal.signal_type == 'SELL' and position > 0:
                    # 卖出
                    proceeds = position * signal.price * (Decimal(1) - self.commission_rate)
                    capital += proceeds

                    trades.append({
                        'date': signal.date,
                        'type': 'SELL',
                        'shares': position,
                        'price': signal.price,
                        'proceeds': proceeds,
                        'reason': signal.reason
                    })
                    position = 0
                    buy_price = None  # 清除买入价格记录

        # 计算最终价值
        final_price = data[-1].close if data else Decimal(0)
        final_value = capital + position * final_price

        # 统计止损次数
        stop_loss_trades = [t for t in trades if t['type'] == 'STOP_LOSS']

        return {
            'initial_capital': self.initial_capital,
            'final_value': final_value,
            'total_return': (final_value - self.initial_capital) / self.initial_capital,
            'trades': trades,
            'final_position': position,
            'stop_loss_count': len(stop_loss_trades),
            'stop_loss_pct': float(self.stop_loss_pct)
        }
