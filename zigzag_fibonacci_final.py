#!/usr/bin/env python3
"""
ZigZag Fibonacci 策略最终完整示例

这是一个基于ZigZag和Fibonacci回撤的完整交易策略实现
"""

import sys
import os
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_backtest.data_models import StockData
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator

def create_realistic_market_data():
    """创建模拟真实市场的股票数据"""
    base_date = datetime(2024, 1, 1)
    
    # 模拟一个完整的市场周期：上涨趋势 + 多次Fibonacci回撤
    price_patterns = [
        # 第一波上涨：100 -> 130 (30%涨幅)
        [100, 102, 105, 108, 112, 115, 118, 122, 125, 128, 130],
        
        # 第一次回撤：130 -> 120 (约38.2%回撤)
        [128, 125, 122, 120],
        
        # 第二波上涨：120 -> 145 (继续上涨)
        [122, 125, 128, 132, 135, 138, 142, 145],
        
        # 第二次回撤：145 -> 135 (约50%回撤)
        [143, 140, 137, 135],
        
        # 第三波上涨：135 -> 160 (新高)
        [137, 140, 143, 147, 150, 153, 157, 160],
        
        # 主要回撤：160 -> 125 (约61.8%回撤)
        [158, 155, 152, 148, 145, 142, 138, 135, 132, 128, 125],
        
        # 反弹：125 -> 140
        [127, 130, 133, 136, 140],
        
        # 继续下跌：140 -> 110
        [138, 135, 132, 128, 125, 122, 118, 115, 112, 110],
        
        # 底部反弹：110 -> 130
        [112, 115, 118, 122, 125, 128, 130]
    ]
    
    # 合并所有价格
    all_prices = []
    for pattern in price_patterns:
        all_prices.extend(pattern)
    
    stock_data = []
    for i, close_price in enumerate(all_prices):
        date = base_date + timedelta(days=i)
        
        # 生成OHLC数据
        if i == 0:
            open_price = close_price
        else:
            prev_close = all_prices[i-1]
            # 开盘价接近前收盘价，有小幅跳空
            gap = (close_price - prev_close) * 0.3
            open_price = prev_close + gap
        
        # 日内高低价
        daily_range = abs(close_price * 0.015)  # 1.5%日内波动
        high_price = max(open_price, close_price) + daily_range * 0.6
        low_price = min(open_price, close_price) - daily_range * 0.6
        
        # 成交量（价格波动大时成交量增加）
        if i > 0:
            price_change = abs(close_price - all_prices[i-1]) / all_prices[i-1]
            volume = int(1000000 * (1 + price_change * 3))
        else:
            volume = 1000000
        
        stock_data.append(StockData(
            symbol="MARKET_SIM",
            date=date,
            open=Decimal(str(round(open_price, 2))),
            high=Decimal(str(round(high_price, 2))),
            low=Decimal(str(round(low_price, 2))),
            close=Decimal(str(round(close_price, 2))),
            volume=volume
        ))
    
    return stock_data

def run_strategy_backtest(stock_data, strategy):
    """运行策略回测"""
    initial_capital = Decimal('100000')
    capital = initial_capital
    position = 0
    trades = []
    
    # 生成信号
    signals = strategy.generate_signals(stock_data)
    
    # 执行交易
    for signal in signals:
        if signal.signal_type == 'BUY' and capital > 0:
            # 买入：使用80%的可用资金
            available_capital = capital * Decimal('0.8')
            shares = int(available_capital / signal.price)
            if shares > 0:
                cost = shares * signal.price
                capital -= cost
                position += shares
                trades.append({
                    'date': signal.date,
                    'type': 'BUY',
                    'shares': shares,
                    'price': signal.price,
                    'cost': cost,
                    'reason': signal.reason,
                    'confidence': signal.confidence
                })
        
        elif signal.signal_type == 'SELL' and position > 0:
            # 卖出：卖出所有持仓
            proceeds = position * signal.price
            capital += proceeds
            trades.append({
                'date': signal.date,
                'type': 'SELL',
                'shares': position,
                'price': signal.price,
                'proceeds': proceeds,
                'reason': signal.reason,
                'confidence': signal.confidence
            })
            position = 0
    
    # 计算最终价值
    final_price = stock_data[-1].close
    final_value = capital + position * final_price
    
    return {
        'signals': signals,
        'trades': trades,
        'initial_capital': initial_capital,
        'final_value': final_value,
        'final_cash': capital,
        'final_position': position,
        'total_return': (final_value - initial_capital) / initial_capital
    }

def main():
    """主函数"""
    print("=== ZigZag Fibonacci 交易策略 ===\n")
    
    # 1. 创建市场数据
    print("1. 创建市场模拟数据...")
    stock_data = create_realistic_market_data()
    print(f"   数据点数: {len(stock_data)}")
    print(f"   时间跨度: {stock_data[0].date.strftime('%Y-%m-%d')} 到 {stock_data[-1].date.strftime('%Y-%m-%d')}")
    print(f"   价格范围: ¥{float(min(d.close for d in stock_data)):.2f} - ¥{float(max(d.close for d in stock_data)):.2f}")
    
    # 2. 初始化策略
    print("\n2. 初始化ZigZag Fibonacci策略...")
    strategy = ZigZagFibonacciSignalGenerator(
        zigzag_threshold=0.04,  # 4%阈值，更敏感
        fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
    )
    print(f"   ZigZag阈值: {float(strategy.zigzag_threshold):.1%}")
    print(f"   Fibonacci水平: {[f'{level:.1%}' for level in strategy.fib_levels]}")
    
    # 3. 分析市场结构
    print("\n3. 分析市场结构...")
    zigzag_points = strategy._calculate_zigzag(stock_data)
    print(f"   识别出 {len(zigzag_points)} 个关键转折点")
    
    if zigzag_points:
        print("   主要转折点:")
        for i, (idx, price, point_type) in enumerate(zigzag_points):
            date = stock_data[idx].date
            print(f"   {i+1:2d}. {date.strftime('%m-%d')} ¥{float(price):6.2f} ({point_type})")
    
    # 4. 运行回测
    print("\n4. 运行策略回测...")
    results = run_strategy_backtest(stock_data, strategy)
    
    # 5. 显示结果
    print("\n5. 回测结果:")
    print("-" * 60)
    print(f"初始资金: ¥{float(results['initial_capital']):,.2f}")
    print(f"最终价值: ¥{float(results['final_value']):,.2f}")
    print(f"总收益率: {float(results['total_return']):.2%}")
    print(f"生成信号: {len(results['signals'])}个")
    print(f"执行交易: {len(results['trades'])}次")
    
    # 6. 信号分析
    if results['signals']:
        print("\n6. 交易信号分析:")
        buy_signals = [s for s in results['signals'] if s.signal_type == 'BUY']
        sell_signals = [s for s in results['signals'] if s.signal_type == 'SELL']
        print(f"   买入信号: {len(buy_signals)}个")
        print(f"   卖出信号: {len(sell_signals)}个")
        
        print("\n   信号详情:")
        for i, signal in enumerate(results['signals']):
            print(f"   {i+1:2d}. {signal.date.strftime('%m-%d')} {signal.signal_type:4s} "
                  f"¥{float(signal.price):6.2f} - {signal.reason} (置信度: {signal.confidence:.2f})")
    
    # 7. 交易分析
    if results['trades']:
        print("\n7. 交易执行分析:")
        for i, trade in enumerate(results['trades']):
            if trade['type'] == 'BUY':
                print(f"   {i+1:2d}. {trade['date'].strftime('%m-%d')} 买入 {trade['shares']:,}股 "
                      f"@ ¥{float(trade['price']):.2f} (成本: ¥{float(trade['cost']):,.2f})")
            else:
                print(f"   {i+1:2d}. {trade['date'].strftime('%m-%d')} 卖出 {trade['shares']:,}股 "
                      f"@ ¥{float(trade['price']):.2f} (收入: ¥{float(trade['proceeds']):,.2f})")
        
        # 计算交易盈亏
        print("\n   交易盈亏:")
        total_profit = Decimal('0')
        trade_count = 0
        
        for i in range(0, len(results['trades']), 2):
            if i + 1 < len(results['trades']):
                buy_trade = results['trades'][i]
                sell_trade = results['trades'][i + 1]
                if buy_trade['type'] == 'BUY' and sell_trade['type'] == 'SELL':
                    profit = sell_trade['proceeds'] - buy_trade['cost']
                    profit_pct = profit / buy_trade['cost']
                    total_profit += profit
                    trade_count += 1
                    print(f"   交易{trade_count}: ¥{float(profit):,.2f} ({float(profit_pct):.2%})")
        
        if trade_count > 0:
            avg_profit = total_profit / trade_count
            print(f"   平均每笔交易盈亏: ¥{float(avg_profit):,.2f}")
    
    # 8. 基准比较
    print("\n8. 策略评估:")
    buy_hold_return = (float(stock_data[-1].close) - float(stock_data[0].close)) / float(stock_data[0].close)
    strategy_return = float(results['total_return'])
    
    print(f"   买入持有收益率: {buy_hold_return:.2%}")
    print(f"   策略收益率: {strategy_return:.2%}")
    print(f"   超额收益: {strategy_return - buy_hold_return:.2%}")
    
    if strategy_return > buy_hold_return:
        print("   ✓ 策略跑赢买入持有")
    else:
        print("   ✗ 策略跑输买入持有")
    
    # 9. 策略总结
    print(f"\n9. 策略总结:")
    print(f"   • ZigZag识别了 {len(zigzag_points)} 个关键转折点")
    print(f"   • 基于Fibonacci回撤生成了 {len(results['signals'])} 个交易信号")
    print(f"   • 执行了 {len(results['trades'])} 次交易")
    print(f"   • 最终收益率: {strategy_return:.2%}")
    
    if len(results['signals']) > 0:
        avg_confidence = sum(s.confidence for s in results['signals']) / len(results['signals'])
        print(f"   • 平均信号置信度: {avg_confidence:.2f}")
    
    print(f"\n策略特点:")
    print(f"   • 使用ZigZag过滤市场噪音，识别真正的趋势转折")
    print(f"   • 基于Fibonacci回撤水平寻找最佳入场点")
    print(f"   • 结合价格动量确认信号有效性")
    print(f"   • 适合趋势性较强的市场环境")

if __name__ == "__main__":
    main()
