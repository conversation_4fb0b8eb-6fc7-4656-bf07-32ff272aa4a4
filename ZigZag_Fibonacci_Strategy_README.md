# ZigZag Fibonacci 交易策略

## 策略概述

这是一个基于ZigZag指标和Fibonacci回撤水平的股票交易策略。该策略通过识别股价的关键转折点，并在Fibonacci回撤水平附近寻找交易机会。

## 策略原理

### 1. ZigZag 指标
- **作用**: 过滤市场噪音，识别真正的价格趋势转折点
- **计算方法**: 当价格变化超过设定阈值（如5%）时，标记为转折点
- **输出**: 一系列高点(HIGH)和低点(LOW)的转折点

### 2. Fibonacci 回撤
- **理论基础**: 基于斐波那契数列的黄金分割比例
- **关键水平**: 23.6%, 38.2%, 50.0%, 61.8%, 78.6%
- **应用**: 在趋势回撤时寻找支撑/阻力位

### 3. 信号生成逻辑

#### 买入信号（上涨趋势中的回撤）
1. 识别从低点到高点的上涨趋势
2. 计算从高点的Fibonacci回撤水平
3. 当价格回撤到Fibonacci水平附近时
4. 检查是否有看涨动量（收盘价 > 开盘价）
5. 生成买入信号

#### 卖出信号（下跌趋势中的反弹）
1. 识别从高点到低点的下跌趋势
2. 计算从低点的Fibonacci反弹水平
3. 当价格反弹到Fibonacci水平附近时
4. 检查是否有看跌动量（收盘价 < 开盘价）
5. 生成卖出信号

## 代码实现

### 核心类：`ZigZagFibonacciSignalGenerator`

```python
class ZigZagFibonacciSignalGenerator:
    def __init__(self, zigzag_threshold=0.05, fib_levels=None):
        self.zigzag_threshold = Decimal(str(zigzag_threshold))
        self.fib_levels = fib_levels or [0.236, 0.382, 0.5, 0.618, 0.786]
```

### 主要方法

1. **`_calculate_zigzag()`**: 计算ZigZag转折点
2. **`_check_fibonacci_signal()`**: 检查Fibonacci回撤信号
3. **`generate_signals()`**: 生成交易信号

## 使用示例

### 基本用法

```python
from stock_backtest.signal_generator import ZigZagFibonacciSignalGenerator

# 初始化策略
strategy = ZigZagFibonacciSignalGenerator(
    zigzag_threshold=0.05,  # 5%阈值
    fib_levels=[0.236, 0.382, 0.5, 0.618, 0.786]
)

# 生成信号
signals = strategy.generate_signals(stock_data)
```

### 完整回测示例

运行以下脚本查看完整示例：

```bash
python zigzag_fibonacci_final.py
```

## 测试脚本

我们提供了多个测试脚本来验证策略：

1. **`zigzag_fibonacci_example.py`**: 基础示例
2. **`debug_zigzag.py`**: ZigZag算法调试
3. **`debug_fibonacci_signals.py`**: 信号生成调试
4. **`zigzag_fibonacci_final.py`**: 完整策略演示

## 策略特点

### 优势
- **趋势识别**: ZigZag有效过滤噪音，识别真正的趋势
- **精确入场**: Fibonacci水平提供精确的入场点
- **风险控制**: 基于技术分析的客观信号
- **适应性强**: 可调整参数适应不同市场环境

### 局限性
- **滞后性**: ZigZag是滞后指标，可能错过早期机会
- **假信号**: 在震荡市场中可能产生较多假信号
- **参数敏感**: 阈值设置对结果影响较大

## 参数调优

### ZigZag阈值
- **较小值(3-5%)**: 更敏感，捕捉更多转折点，但噪音较多
- **较大值(6-10%)**: 更稳定，减少假信号，但可能错过机会

### Fibonacci水平
- **标准水平**: 23.6%, 38.2%, 50.0%, 61.8%, 78.6%
- **重点关注**: 38.2%和61.8%（黄金分割比例）

### 价格容忍度
- **当前设置**: 3%（价格接近Fibonacci水平的容忍范围）
- **可调整**: 根据股票波动性调整

## 实际应用建议

1. **市场环境**: 适合趋势性较强的市场
2. **时间周期**: 建议用于日线或更长周期
3. **风险管理**: 结合止损和仓位管理
4. **组合使用**: 可与其他技术指标组合使用

## 扩展功能

可以考虑添加以下功能：

1. **动态阈值**: 根据市场波动性调整ZigZag阈值
2. **多时间框架**: 结合不同时间周期的信号
3. **成交量确认**: 加入成交量分析
4. **止损机制**: 自动止损功能
5. **信号过滤**: 更复杂的信号过滤条件

## 总结

ZigZag Fibonacci策略是一个基于经典技术分析理论的量化交易策略。通过结合趋势识别和精确的入场点选择，为投资者提供了一个系统化的交易方法。

虽然策略在某些市场条件下可能表现不佳，但其核心思想和实现方法为进一步的策略开发提供了良好的基础。

---

**注意**: 本策略仅供学习和研究使用，实际投资请谨慎评估风险。
